{"greeting": "Benvingut a <PERSON>lu", "task": {"title": "Tasca", "seeMore": "<PERSON><PERSON><PERSON> més", "seeLess": "<PERSON><PERSON><PERSON> menys", "tokens": "Tokens:", "cache": "Caché:", "apiCost": "Cost d'API:", "contextWindow": "Finestra de context:", "closeAndStart": "<PERSON><PERSON> tasca i iniciar-ne una de nova", "export": "Exportar historial de tasques", "delete": "Eliminar tasca (Shift + Clic per ometre confirmació)", "condenseContext": "Condensar context de forma intel·ligent", "share": "Compartir tasca", "copyId": "Copiar ID de la tasca", "shareWithOrganization": "Compartir amb l'organització", "shareWithOrganizationDescription": "Només els membres de la teva organització poden accedir", "sharePublicly": "Compartir públicament", "sharePubliclyDescription": "Qualsevol amb l'enllaç pot accedir", "connectToCloud": "Connecta al núvol", "connectToCloudDescription": "Inicia sessió a zhanlu Cloud per compartir tasques", "sharingDisabledByOrganization": "Compartició deshabilitada per l'organització", "shareSuccessOrganization": "Enllaç d'organització copiat al porta-retalls", "shareSuccessPublic": "Enllaç públic copiat al porta-retalls"}, "unpin": "Desfixar", "pin": "Fixar", "tokenProgress": {"availableSpace": "Espai disponible: {{amount}} tokens", "tokensUsed": "Tokens utilitzats: {{used}} de {{total}}", "reservedForResponse": "Reservat per a resposta del model: {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON> a intentar", "tooltip": "Torna a provar l'operació"}, "startNewTask": {"title": "Començar una nova tasca", "tooltip": "Comença una nova tasca"}, "proceedAnyways": {"title": "Con<PERSON><PERSON><PERSON> de totes maneres", "tooltip": "Continua mentre s'executa l'ordre"}, "save": {"title": "Desar", "tooltip": "Desa els canvis del missatge"}, "reject": {"title": "Rebutjar", "tooltip": "Rebutja aquesta acció"}, "completeSubtaskAndReturn": "Completar la subtasca i tornar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprova aquesta acció"}, "runCommand": {"title": "Executar ordre", "tooltip": "Executa aquesta ordre"}, "proceedWhileRunning": {"title": "Continuar mentre s'executa", "tooltip": "Continua malgrat els advertiments"}, "killCommand": {"title": "Atura l'ordre", "tooltip": "Atura l'ordre actual"}, "resumeTask": {"title": "Reprendre la tasca", "tooltip": "<PERSON><PERSON> la tasca actual"}, "terminate": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Finalitz<PERSON> la tasca actual"}, "cancel": {"title": "Cancel·lar", "tooltip": "Cancel·la l'operació actual"}, "scrollToBottom": "Desplaça't al final del xat", "about": "<PERSON><PERSON>, co<PERSON><PERSON><PERSON>, refactoritza i depura codi amb l'ajuda del Model d'IA Zhanlu.<br />Consulta la nostra <DocsLink>documentació</DocsLink> per a més informació.", "onboarding": "La llista de tasques en aquest espai de treball està buida. Comença introduint la teva tasca a sota.<br>No saps com començar? Llegeix més a la nostra <DocsLink>documentació</DocsLink>.", "zhanluTips": {"boomerangTasks": {"title": "Orquestració de Tasques", "description": "Divideix les tasques en parts més petites i manejables."}, "stickyModels": {"title": "Modes persistents", "description": "Cada mode recorda el vostre darrer model utilitzat"}, "tools": {"title": "<PERSON><PERSON>", "description": "Permet que la IA resolgui problemes navegant per la web, executant ordres i molt més."}, "customizableModes": {"title": "Modes personalitzables", "description": "Personalitats especialitzades amb comportaments propis i models assignats"}, "architect": {"title": "Mode Arquitecte", "description": "Planifica l'arquitectura del sistema i dissenya estructures de codi."}, "code": {"title": "Mode Codi", "description": "Genera, modifica i optimitza codi."}, "unit_test": {"title": "Mode Proves Unitàries", "description": "<PERSON>rea proves exhaustives per garantir l'estabilitat del codi."}, "project_fix": {"title": "Mode Reparació de Projecte", "description": "Identifica i repara problemes o errors en el projecte."}, "security_fix": {"title": "Mode Reparació de Seguretat", "description": "Identifica i resol vulnerabilitats de seguretat en el codi."}, "code_review": {"title": "Mode Revisió de Codi", "description": "Analitza la qualitat del codi i ofereix suggeriments de millora."}, "documentation": {"title": "Mode Documentació", "description": "Crea documentació i instruccions clares i detallades."}, "qa": {"title": "Mode Preguntes i Respostes", "description": "Respon preguntes i proporciona assistència senzilla."}}, "selectMode": "Selecciona el mode d'interacció", "selectApiConfig": "Seleccioneu la configuració de l'API", "internetSearch": "Després d'obrir la cerca a Internet, podreu cercar contingut relacionat a Internet", "internetSearchClosed": "Tanca la cerca a Internet", "enhancePrompt": "Millora la sol·licitud amb context addicional", "addImages": "Afegeix imatges al missatge", "sendMessage": "Envia el missatge", "stopTts": "Atura la síntesi de veu", "typeMessage": "Escriu un missatge...", "typeTask": "Escriu la teva tasca aquí...", "addContext": "@ afegeix context, / mode de canvi, # dreceres", "dragFiles": "Mantén premut Shift per arrossegar fitxers", "dragFilesImages": "Mantén premut Shift per arrossegar fitxers/imatges", "enhancePromptDescription": "El botó 'Millora la sol·licitud' ajuda a millorar la teva sol·licitud proporcionant context addicional, aclariments o reformulacions. Prova d'escriure una sol·licitud aquí i fes clic al botó de nou per veure com funciona.", "modeSelector": {"title": "Modes", "marketplace": "Marketplace de Modes", "settings": "Configuració de Modes", "description": "Personalitats especialitzades que adapten el comportament de Roo."}, "errorReadingFile": "Error en llegir el fitxer:", "noValidImages": "No s'ha processat cap imatge vàlida", "separator": "Separador", "edit": "Edita...", "forNextMode": "per al següent mode", "forPreviousMode": "per al mode anterior", "error": "Error", "warning": "<PERSON><PERSON><PERSON>", "diffError": {"title": "Ed<PERSON><PERSON> fall<PERSON>"}, "troubleMessage": "<PERSON><PERSON><PERSON> està tenint problemes...", "apiRequest": {"title": "Sol·licitud API", "failed": "Sol·licitud API ha fallat", "streaming": "Sol·licitud API...", "cancelled": "Sol·licitud API cancel·lada", "streamingFailed": "Transmissió API ha fallat"}, "checkpoint": {"initial": "Punt de control inicial", "regular": "Punt de control", "initializingWarning": "Encara s'està inicialitzant el punt de control... Si això triga massa, pots desactivar els punts de control a la <settingsLink>configuració</settingsLink> i reiniciar la teva tasca.", "menu": {"viewDiff": "Veure diferències", "restore": "Restaurar punt de control", "restoreFiles": "<PERSON><PERSON><PERSON> arxius", "restoreFilesDescription": "Restaura els arxius del teu projecte a una instantània presa en aquest punt.", "restoreFilesAndTask": "Restaurar arxius i tasca", "confirm": "Confirmar", "cancel": "Cancel·lar", "cannotUndo": "Aquesta acció no es pot desfer.", "restoreFilesAndTaskDescription": "Restaura els arxius del teu projecte a una instantània presa en aquest punt i elimina tots els missatges posteriors a aquest punt."}, "current": "Actual"}, "instructions": {"wantsToFetch": "Z<PERSON>lu vol obtenir instruccions detallades per ajudar amb la tasca actual."}, "fileOperations": {"wantsToRead": "Zhanlu vol llegir aquest fitxer:", "wantsToReadOutsideWorkspace": "Zhanlu vol llegir aquest fitxer fora de l'espai de treball:", "didRead": "<PERSON><PERSON><PERSON> ha llegit aquest fitxer:", "wantsToEdit": "Zhanlu vol editar aquest fitxer:", "wantsToEditOutsideWorkspace": "Zhanlu vol editar aquest fitxer fora de l'espai de treball:", "wantsToEditProtected": "zhanlu vol editar un fitxer de configuració protegit:", "wantsToCreate": "<PERSON><PERSON><PERSON> vol crear un nou fitxer:", "wantsToSearchReplace": "Zhanlu vol realitzar cerca i substitució en aquest fitxer:", "didSearchReplace": "Z<PERSON>lu ha realitzat cerca i substitució en aquest fitxer:", "wantsToInsert": "Zhanlu vol inserir contingut en aquest fitxer:", "wantsToInsertWithLineNumber": "<PERSON><PERSON>lu vol inserir contingut a la línia {{lineNumber}} d'aquest fitxer:", "wantsToInsertAtEnd": "Zhanlu vol afegir contingut al final d'aquest fitxer:", "wantsToReadAndXMore": "Z<PERSON>lu vol llegir aquest fitxer i {{count}} més:", "wantsToReadMultiple": "Zhanlu vol llegir diversos fitxers:", "wantsToApplyBatchChanges": "zhanlu vol aplicar canvis a múltiples fitxers:"}, "directoryOperations": {"wantsToViewTopLevel": "Zhanlu vol veure els fitxers de nivell superior en aquest directori:", "didViewTopLevel": "<PERSON><PERSON><PERSON> ha vist els fitxers de nivell superior en aquest directori:", "wantsToViewRecursive": "Zhanlu vol veure recursivament tots els fitxers en aquest directori:", "didViewRecursive": "<PERSON><PERSON><PERSON> ha vist recursivament tots els fitxers en aquest directori:", "wantsToViewDefinitions": "Zhanlu vol veure noms de definicions de codi font utilitzats en aquest directori:", "didViewDefinitions": "<PERSON><PERSON><PERSON> ha vist noms de definicions de codi font utilitzats en aquest directori:", "wantsToSearch": "<PERSON><PERSON>lu vol cercar en aquest directori <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON><PERSON> ha cercat en aquest directori <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Zhanlu vol cercar en aquest directori (fora de l'espai de treball) <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> ha cercat en aquest directori (fora de l'espai de treball) <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Zhanlu vol veure els fitxers de nivell superior en aquest directori (fora de l'espai de treball):", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> ha vist els fitxers de nivell superior en aquest directori (fora de l'espai de treball):", "wantsToViewRecursiveOutsideWorkspace": "Zhanlu vol veure recursivament tots els fitxers en aquest directori (fora de l'espai de treball):", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> ha vist recursivament tots els fitxers en aquest directori (fora de l'espai de treball):", "wantsToViewDefinitionsOutsideWorkspace": "Zhanlu vol veure noms de definicions de codi font utilitzats en aquest directori (fora de l'espai de treball):", "didViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON> ha vist noms de definicions de codi font utilitzats en aquest directori (fora de l'espai de treball):"}, "commandOutput": "Sortida de l'ordre", "commandExecution": {"running": "Executant", "pid": "PID: {{pid}}", "exited": "Finalitzat ({{exitCode}})", "manageCommands": "Gestiona els permisos de les ordres", "commandManagementDescription": "Gestiona els permisos de les ordres: Fes clic a ✓ per permetre l'execució automàtica, ✗ per denegar l'execució. Els patrons es poden activar/desactivar o eliminar de les llistes. <settingsLink>Mostra tots els paràmetres</settingsLink>", "addToAllowed": "Afegeix a la llista de permesos", "removeFromAllowed": "Elimina de la llista de permesos", "addToDenied": "Afegeix a la llista de denegats", "removeFromDenied": "Elimina de la llista de denegats", "abortCommand": "Interromp l'execució de l'ordre", "expandOutput": "Amplia la sortida", "collapseOutput": "Redueix la sortida", "expandManagement": "Amplia la secció de gestió d'ordres", "collapseManagement": "Redueix la secció de gestió d'ordres"}, "response": "Resposta", "arguments": "Arguments", "mcp": {"wantsToUseTool": "Zhanlu vol utilitzar una eina al servidor MCP {{serverName}}:", "wantsToAccessResource": "Zhanlu vol accedir a un recurs al servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "Zhanlu vol canviar a mode <code>{{mode}}</code>", "wantsToSwitchWithReason": "Zhanlu vol canviar a mode <code>{{mode}}</code> perquè: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> ha canviat a mode <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON><PERSON> ha canviat a mode <code>{{mode}}</code> perquè: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> vol crear una nova subtasca en mode <code>{{mode}}</code>:", "wantsToFinish": "Zhanlu vol finalitzar aquesta subtasca", "newTaskContent": "Instruccions de la subtasca", "completionContent": "Subtasca completada", "resultContent": "Resultats de la subtasca", "defaultResult": "Si us plau, continua amb la següent tasca.", "completionInstructions": "Subtasca completada! Pots revisar els resultats i suggerir correccions o següents passos. Si tot sembla correcte, confirma per tornar el resultat a la tasca principal."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> té una pregunta:"}, "taskCompleted": "<PERSON><PERSON> completada", "powershell": {"issues": "Sembla que estàs tenint problemes amb Windows PowerShell, si us plau consulta aquesta documentació per a més informació."}, "autoApprove": {"title": "Aprovació automàtica:", "none": "Cap", "description": "L'aprovació automàtica permet a Zhanlu realitzar accions sense demanar permís. Activa-la només per a accions en les que confies plenament. Configuració més detallada disponible a la <settingsLink>Configuració</settingsLink>.", "selectOptionsFirst": "Selecciona almenys una opció a continuació per activar l'aprovació automàtica", "toggleAriaLabel": "Commuta l'aprovació automàtica", "disabledAriaLabel": "Aprovació automàtica desactivada: seleccioneu primer les opcions"}, "reasoning": {"thinking": "Pensant", "seconds": "{{count}}s"}, "contextCondense": {"title": "Context condensat", "condensing": "Condensant context...", "errorHeader": "Error en condensar el context", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "Copiar a l'entrada (o Shift + clic)", "autoSelectCountdown": "Selecció automàtica en {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Actualització de la versió 2.3.2 de <PERSON>", "description": "Correcció d'errors, autocompleció de codi", "whatsNew": "Actualitzacions importants", "feature1": "<bold>Mode Fundació de Codi afegit</bold>: El mode Fundació de Codi genera exercicis de programació", "feature2": "<bold>Corre<PERSON><PERSON> de bug de tasques d'historial</bold>: <PERSON><PERSON> resolt on les noves tasques apareixen a les tasques d'historial", "feature3": "<bold>Correcció del problema de parpelleig</bold>: Problema de parpelleig ocasional de pantalla en mostrar codi resolt", "feature4": "<bold>Altres optimitzacions</bold>: Optimitzacions de diversos altres problemes", "hideButton": "<PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Consulta la <discordLink>documentac<PERSON>ó detallad<PERSON></discordLink> per conèixer més funcions 🚀"}, "browser": {"rooWantsToUse": "Zhanlu vol utilitzar el navegador:", "consoleLogs": "Registres de consola", "noNewLogs": "(Cap registre nou)", "screenshot": "Captura de pantalla del navegador", "cursor": "cursor", "navigation": {"step": "Pas {{current}} de {{total}}", "previous": "Anterior", "next": "<PERSON><PERSON><PERSON><PERSON>"}, "sessionStarted": "<PERSON><PERSON>ó de navegador iniciada", "actions": {"title": "Acció de navegació: ", "launch": "Iniciar nave<PERSON><PERSON> a {{url}}", "click": "Clic ({{coordinate}})", "type": "Escriure \"{{text}}\"", "scrollDown": "<PERSON>p<PERSON><PERSON><PERSON>", "scrollUp": "<PERSON><PERSON><PERSON><PERSON><PERSON> amunt", "close": "<PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Expandir bloc de codi", "collapse": "Contraure bloc de codi", "enable_wrap": "Activar ajustament de línia", "disable_wrap": "Desactivar ajustament de línia", "copy_code": "Copiar codi"}}, "qucikInstructions": {"UiToCode": "Generació de codi del disseny de la UI", "UmlToCode": "Codi de generació de gràfics UML", "ExplainCode": "Interpretació del codi", "FixCode": "Correcció de codi", "ImproveCode": "Optimització del codi", "UnitTest": "<PERSON><PERSON>", "CODE_REVIEW": "Revisió de codi", "CommentCode": "Comentari de codi", "PlusButtonClicked": "Neteja el diàleg"}, "systemPromptWarning": "ADVERTÈNCIA: S'ha activat una substitució personalitzada d'instruccions del sistema. Això pot trencar greument la funcionalitat i causar un comportament impredictible.", "profileViolationWarning": "El perfil actual no és compatible amb la configuració de la teva organització", "shellIntegration": {"title": "Advertència d'execució d'ordres", "description": "La teva ordre s'està executant sense la integració de shell del terminal VSCode. Per suprimir aquest advertiment, pots desactivar la integració de shell a la secció <strong>Terminal</strong> de la <settingsLink>configurac<PERSON><PERSON> de Z<PERSON>lu</settingsLink> o solucionar problemes d'integració del terminal VSCode utilitzant l'enllaç a continuació.", "troubleshooting": "Fes clic aquí per a la documentació d'integració de shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "S'ha arribat al límit de sol·licituds aprovades automàticament", "description": "z<PERSON><PERSON> ha arribat al límit aprovat automàticament de {{count}} sol·licitud(s) d'API. Vols reiniciar el comptador i continuar amb la tasca?", "button": "Reiniciar i continuar"}}, "codebaseSearch": {"wantsToSearch": "z<PERSON><PERSON> vol cercar a la base de codi <code>{{query}}</code>:", "wantsToSearchWithPath": "z<PERSON><PERSON> vol cercar a la base de codi <code>{{query}}</code> a <code>{{path}}</code>:", "didSearch_one": "S'ha trobat 1 resultat", "didSearch_other": "S'han trobat {{count}} resultats", "resultTooltip": "Puntuació de similitud: {{score}} (fes clic per obrir el fitxer)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON> tot"}, "deny": {"title": "<PERSON><PERSON><PERSON> tot"}}, "indexingStatus": {"ready": "Índex preparat", "indexing": "Indexant {{percentage}}%", "indexed": "Indexat", "error": "<PERSON><PERSON><PERSON>", "status": "Estat de l'índex"}, "versionIndicator": {"ariaLabel": "Versió {{version}} - <PERSON><PERSON> clic per veure les notes de llançament"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> Cloud arribarà aviat!", "description": "Executa agents remots al núvol, accedeix a les teves tasques des de qualsevol lloc, col·labora amb altres i molt més.", "joinWaitlist": "Uneix-te a la llista d'espera per obtenir accés anticipat."}, "editMessage": {"placeholder": "Edita el teu missatge..."}}