{"errorBoundary": {"title": "Alguna cosa ha anat malament", "reportText": "Si us plau, ajuda'ns a millorar informant d'aquest error a", "githubText": "la nostra pàgina d'incidències de GitHub", "copyInstructions": "Copia i enganxa el següent missatge d'error per incloure'l com a part de la teva presentació:", "errorStack": "<PERSON><PERSON>errors:", "componentStack": "Pila de components:"}, "answers": {"yes": "Sí", "no": "No", "cancel": "Cancel·lar", "remove": "Eliminar", "keep": "Mantenir"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "Cerca..."}, "mermaid": {"loading": "Generant diagrama mermaid...", "render_error": "No es pot renderitzar el diagrama", "file_media": "Vista prèvia del procés", "code": "Mostra el codi font", "buttons": {"zoom": "Zoom", "zoomIn": "Ampliar", "zoomOut": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "save": "Desar imatge", "viewCode": "<PERSON>eure codi", "viewDiagram": "<PERSON><PERSON>e diagrama", "close": "<PERSON><PERSON>"}, "modal": {"codeTitle": "Codi Mermaid"}, "tabs": {"diagram": "Diagrama", "code": "Codi"}, "feedback": {"imageCopied": "Imatge copiada al porta-retalls", "copyError": "Error copiant la imatge"}}, "file": {"errors": {"invalidDataUri": "Format d'URI de dades no vàlid", "copyingImage": "Error copiant la imatge: {{error}}", "openingImage": "Error obrint la imatge: {{error}}", "pathNotExists": "El camí no existeix: {{path}}", "couldNotOpen": "No s'ha pogut obrir el fitxer: {{error}}", "couldNotOpenGeneric": "No s'ha pogut obrir el fitxer!"}, "success": {"imageDataUriCopied": "URI de dades de la imatge copiada al porta-retalls"}}, "confirmation": {"deleteMessage": "Eliminar missatge", "deleteWarning": "Eliminar aquest missatge eliminarà tots els missatges posteriors de la conversa. Vols continuar?", "editMessage": "<PERSON><PERSON> miss<PERSON>", "editWarning": "Editar aquest missatge eliminarà tots els missatges posteriors de la conversa. Vols continuar?", "proceed": "<PERSON><PERSON><PERSON><PERSON>"}}