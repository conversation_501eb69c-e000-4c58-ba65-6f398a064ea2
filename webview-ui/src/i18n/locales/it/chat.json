{"greeting": "Benvenuto a Zhanlu", "task": {"title": "Attività", "seeMore": "<PERSON><PERSON><PERSON>", "seeLess": "<PERSON><PERSON><PERSON> meno", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "Costo API:", "contextWindow": "Lunghezza del contesto:", "closeAndStart": "<PERSON>udi attività e iniziane una nuova", "export": "Esporta cronologia attività", "delete": "Elimina attività (Shift + Clic per saltare la conferma)", "condenseContext": "Condensa contesto in modo intelligente", "share": "Condividi attività", "copyId": "Copia ID attività", "shareWithOrganization": "Condividi con l'organizzazione", "shareWithOrganizationDescription": "Solo i membri della tua organizzazione possono accedere", "sharePublicly": "Condividi pubblicamente", "sharePubliclyDescription": "Chiunque con il link può accedere", "connectToCloud": "<PERSON><PERSON><PERSON> al <PERSON>", "connectToCloudDescription": "Accedi a zhanlu Cloud per condividere attività", "sharingDisabledByOrganization": "Condivisione disabilitata dall'organizzazione", "shareSuccessOrganization": "Link organizzazione copiato negli appunti", "shareSuccessPublic": "Link pubblico copiato negli appunti"}, "unpin": "Rilascia", "pin": "<PERSON><PERSON>", "tokenProgress": {"availableSpace": "Spazio disponibile: {{amount}} tokens", "tokensUsed": "Tokens utilizzati: {{used}} di {{total}}", "reservedForResponse": "Riservato per risposta del modello: {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Prova di nuovo l'operazione"}, "startNewTask": {"title": "Inizia nuova attività", "tooltip": "Inizia una nuova attività"}, "proceedAnyways": {"title": "Procedi comunque", "tooltip": "Continua mentre il comando è in esecuzione"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "Salva le modifiche del messaggio"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Rifiuta questa azione"}, "completeSubtaskAndReturn": "Completa sottoattività e torna indietro", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Approva questa azione"}, "runCommand": {"title": "<PERSON><PERSON><PERSON><PERSON> comando", "tooltip": "Esegui questo comando"}, "proceedWhileRunning": {"title": "Procedi durante l'esecuzione", "tooltip": "Continua nonostante gli avvisi"}, "killCommand": {"title": "Termina comando", "tooltip": "Termina il comando corrente"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Continua l'attività corrente"}, "terminate": {"title": "Termina", "tooltip": "Termina l'attività corrente"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Annulla l'operazione corrente"}, "scrollToBottom": "<PERSON><PERSON><PERSON> fino in fondo alla chat", "about": "<PERSON><PERSON>, co<PERSON><PERSON><PERSON>, refactorizza e esegui il debug del codice con l'aiuto del modello IA Zhanlu.<br />Consulta la nostra <DocsLink>documentazione</DocsLink> per maggiori informazioni.", "onboarding": "L'elenco delle attività in questo workspace è vuoto. Inizia inserendo la tua attività qui sotto.<br>Non sei sicuro di come iniziare? Leggi la nostra <DocsLink>documentazione</DocsLink>.", "zhanluTips": {"architectMode": {"title": "Modalità Architetto", "description": "Crea piani dettagliati per implementare soluzioni"}, "codeMode": {"title": "Modalità Codice", "description": "Scrive e modifica codice secondo le migliori pratiche"}, "testMode": {"title": "Modalità Test Unitario", "description": "Genera test completi per il tuo codice"}, "projectFixMode": {"title": "Modalità Correzione Progetto", "description": "Identifica e ripara i difetti nei progetti"}, "sastMode": {"title": "Modalità Correzione Sicurezza", "description": "Corregge le vulnerabilità di sicurezza nel codice"}, "codeReviewMode": {"title": "Modalità Revisione Codice", "description": "Valuta la qualità del codice e suggerisce miglioramenti"}, "readmeMode": {"title": "Modalità Documentazione", "description": "Crea documentazione tecnica dettagliata"}, "simpleMode": {"title": "Modalità Domande e Risposte", "description": "Risponde con precisione alle domande tecniche"}, "boomerangTasks": {"title": "Orchestrazione di Attività", "description": "Dividi le attività in parti più piccole e gestibili."}, "stickyModels": {"title": "Modalità persistenti", "description": "Ogni modalità ricorda il tuo ultimo modello utilizzato"}, "tools": {"title": "Strumenti", "description": "Consenti all'IA di risolvere i problemi navigando sul Web, eseguendo comandi e altro ancora."}, "customizableModes": {"title": "Modalità personalizzabili", "description": "Personalità specializzate con comportamenti propri e modelli assegnati"}}, "selectMode": "Seleziona modalità di interazione", "selectApiConfig": "Seleziona la configurazione API", "internetSearch": "Dopo aver aperto la ricerca su Internet, puoi cercare contenuti correlati su Internet", "internetSearchClosed": "Chiudi la ricerca su Internet", "enhancePrompt": "Migliora prompt con contesto aggiuntivo", "addImages": "Aggiungi immagini al messaggio", "sendMessage": "Invia messaggio", "stopTts": "Interrompi sintesi vocale", "typeMessage": "Scrivi un messaggio...", "typeTask": "Scrivi la tua attività qui...", "addContext": "@ Aggiungi contesto, / modalità di commutazione, # comandi rapidi", "dragFiles": "Tieni premuto Shift e trascina i file", "dragFilesImages": "Tieni premuto Shift e trascina file/immagini", "enhancePromptDescription": "Il pulsante 'Migliora prompt' aiuta a migliorare la tua richiesta fornendo contesto aggiuntivo, chiarimenti o riformulazioni. Prova a digitare una richiesta qui e fai di nuovo clic sul pulsante per vedere come funziona.", "modeSelector": {"title": "Modalità", "marketplace": "Marketplace delle Modalità", "settings": "Impostazioni Modalità", "description": "Personalità specializzate che adattano il comportamento di Roo."}, "errorReadingFile": "Errore nella lettura del file:", "noValidImages": "<PERSON>essuna immagine valida è stata elaborata", "separator": "Separatore", "edit": "Modifica...", "forNextMode": "per la prossima modalità", "forPreviousMode": "per la modalità precedente", "instructions": {"wantsToFetch": "Zhanlu vuole recuperare istruzioni dettagliate per aiutare con l'attività corrente"}, "error": "Errore", "warning": "Avviso", "diffError": {"title": "Modifica non riuscita"}, "troubleMessage": "<PERSON><PERSON><PERSON> sta avendo problemi...", "apiRequest": {"title": "Richiesta API", "failed": "Richiesta API fallita", "streaming": "Richiesta API...", "cancelled": "Richiesta API annullata", "streamingFailed": "Streaming API fallito"}, "checkpoint": {"initial": "Checkpoint iniziale", "regular": "Checkpoint", "initializingWarning": "Inizializzazione del checkpoint in corso... Se questa operazione richiede troppo tempo, puoi disattivare i checkpoint nelle <settingsLink>impostazioni</settingsLink> e riavviare l'attività.", "menu": {"viewDiff": "Visualizza differenze", "restore": "Ripristina checkpoint", "restoreFiles": "R<PERSON><PERSON><PERSON> file", "restoreFilesDescription": "Rip<PERSON><PERSON> i file del tuo progetto a uno snapshot catturato in questo punto.", "restoreFilesAndTask": "Ripristina file e attività", "confirm": "Conferma", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Questa azione non può essere annullata.", "restoreFilesAndTaskDescription": "R<PERSON><PERSON><PERSON> i file del tuo progetto a uno snapshot catturato in questo punto ed elimina tutti i messaggi successivi a questo punto."}, "current": "<PERSON><PERSON><PERSON>"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON><PERSON> vuole leggere questo file:", "wantsToReadOutsideWorkspace": "Zhanlu vuole leggere questo file al di fuori dell'area di lavoro:", "didRead": "<PERSON><PERSON><PERSON> ha letto questo file:", "wantsToEdit": "Z<PERSON>lu vuole modificare questo file:", "wantsToEditOutsideWorkspace": "Zhanlu vuole modificare questo file al di fuori dell'area di lavoro:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> vuole modificare un file di configurazione protetto:", "wantsToCreate": "<PERSON><PERSON><PERSON> vuole creare un nuovo file:", "wantsToSearchReplace": "Zhanlu vuole eseguire ricerca e sostituzione in questo file:", "didSearchReplace": "Zhanlu ha eseguito ricerca e sostituzione in questo file:", "wantsToInsert": "<PERSON><PERSON><PERSON> vuole inserire contenuto in questo file:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> vuole inserire contenuto in questo file alla riga {{lineNumber}}:", "wantsToInsertAtEnd": "<PERSON><PERSON><PERSON> vuole aggiungere contenuto alla fine di questo file:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> vuole leggere questo file e altri {{count}}:", "wantsToReadMultiple": "<PERSON><PERSON><PERSON> vuole leggere più file:", "wantsToApplyBatchChanges": "<PERSON><PERSON><PERSON> vuole applicare modifiche a più file:"}, "directoryOperations": {"wantsToViewTopLevel": "Zhanlu vuole visualizzare i file di primo livello in questa directory:", "didViewTopLevel": "<PERSON><PERSON><PERSON> ha visualizzato i file di primo livello in questa directory:", "wantsToViewRecursive": "Zhanlu vuole visualizzare ricorsivamente tutti i file in questa directory:", "didViewRecursive": "<PERSON><PERSON><PERSON> ha visualizzato ricorsivamente tutti i file in questa directory:", "wantsToViewDefinitions": "Zhanlu vuole visualizzare i nomi delle definizioni di codice sorgente utilizzate in questa directory:", "didViewDefinitions": "<PERSON><PERSON><PERSON> ha visualizzato i nomi delle definizioni di codice sorgente utilizzate in questa directory:", "wantsToSearch": "<PERSON><PERSON><PERSON> vuole cercare in questa directory <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON><PERSON> ha cercato in questa directory <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> vuole cercare in questa directory (fuori dall'area di lavoro) <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> ha cercato in questa directory (fuori dall'area di lavoro) <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Zhanlu vuole visualizzare i file di primo livello in questa directory (fuori dall'area di lavoro):", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> ha visualizzato i file di primo livello in questa directory (fuori dall'area di lavoro):", "wantsToViewRecursiveOutsideWorkspace": "Zhanlu vuole visualizzare ricorsivamente tutti i file in questa directory (fuori dall'area di lavoro):", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> ha visualizzato ricorsivamente tutti i file in questa directory (fuori dall'area di lavoro):", "wantsToViewDefinitionsOutsideWorkspace": "Zhanlu vuole visualizzare i nomi delle definizioni di codice sorgente utilizzate in questa directory (fuori dall'area di lavoro):", "didViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON> ha visualizzato i nomi delle definizioni di codice sorgente utilizzate in questa directory (fuori dall'area di lavoro):"}, "commandOutput": "Output del comando", "commandExecution": {"running": "In esecuzione", "pid": "PID: {{pid}}", "exited": "Terminato ({{exitCode}})", "manageCommands": "Gestisci autorizzazioni comandi", "commandManagementDescription": "Gestisci le autorizzazioni dei comandi: fai clic su ✓ per consentire l'esecuzione automatica, ✗ per negare l'esecuzione. I pattern possono essere attivati/disattivati o rimossi dagli elenchi. <settingsLink>Visualizza tutte le impostazioni</settingsLink>", "addToAllowed": "Aggiungi all'elenco consentiti", "removeFromAllowed": "<PERSON><PERSON><PERSON><PERSON> dall'elenco consentiti", "addToDenied": "Aggiungi all'elenco negati", "removeFromDenied": "<PERSON><PERSON><PERSON><PERSON> dall'elenco negati", "abortCommand": "Interrompi esecuzione comando", "expandOutput": "Espandi output", "collapseOutput": "Comprimi output", "expandManagement": "Espandi la sezione di gestione dei comandi", "collapseManagement": "Comprimi la sezione di gestione dei comandi"}, "response": "Risposta", "arguments": "Argomenti", "mcp": {"wantsToUseTool": "<PERSON><PERSON>lu vuole utilizzare uno strumento sul server MCP {{serverName}}:", "wantsToAccessResource": "<PERSON><PERSON><PERSON> vuole accedere a una risorsa sul server MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> vuole passare alla modalità <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> vuole passare alla modalità <code>{{mode}}</code> perché: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> è passato alla modalità <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON><PERSON> è passato alla modalità <code>{{mode}}</code> perché: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> vuole creare una nuova sottoattività in modalità <code>{{mode}}</code>:", "wantsToFinish": "<PERSON><PERSON><PERSON> vuole completare questa sottoattività", "newTaskContent": "Istruzioni sottoattività", "completionContent": "Sottoattività completata", "resultContent": "Risultati sottoattività", "defaultResult": "Per favore continua con la prossima attività.", "completionInstructions": "Sottoattività completata! Puoi rivedere i risultati e suggerire correzioni o prossimi passi. Se tutto sembra a posto, conferma per restituire il risultato all'attività principale."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> ha una domanda:"}, "taskCompleted": "Attività completata", "powershell": {"issues": "<PERSON><PERSON>ra che tu stia avendo problemi con Windows PowerShell, consulta questa"}, "autoApprove": {"title": "Auto-approvazione:", "none": "Nessuna", "description": "L'auto-approvazione permette a zhanlu di eseguire azioni senza chiedere permesso. Abilita solo per azioni di cui ti fidi completamente. Configurazione più dettagliata disponibile nelle <settingsLink>Impostazioni</settingsLink>.", "selectOptionsFirst": "Seleziona almeno un'opzione qui sotto per abilitare l'auto-approvazione", "toggleAriaLabel": "Attiva/disattiva approvazione automatica", "disabledAriaLabel": "Approvazione automatica disabilitata - seleziona prima le opzioni"}, "reasoning": {"thinking": "<PERSON><PERSON>", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contesto condensato", "condensing": "Condensazione del contesto...", "errorHeader": "Impossibile condensare il contesto", "tokens": "token"}, "followUpSuggest": {"copyToInput": "Copia nell'input (o Shift + clic)", "autoSelectCountdown": "Selezione automatica in {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Aggiornamento versione 2.3.2 di <PERSON>lu", "description": "Correzione bug, completamento del codice", "whatsNew": "Aggiornamenti importanti", "feature1": "<bold>Modalità Fondazione Codice aggiunta</bold>: La modalità Fondazione Codice genera esercizi di programmazione", "feature2": "<bold>Correzione bug attività cronologia</bold>: Problema risolto dove le nuove attività appaiono nelle attività cronologia", "feature3": "<bold>Correzione problema sfarfallio</bold>: Problema di sfarfallio occasionale dello schermo durante la visualizzazione del codice risolto", "feature4": "<bold><PERSON><PERSON> ottimi<PERSON>ni</bold>: Ottimizzazioni di vari altri problemi", "hideButton": "Nascondi annuncio", "detailsDiscussLinks": "Consulta la <discordLink>documentazione dettagliata</discordLink> per scoprire più funzionalità 🚀"}, "browser": {"rooWantsToUse": "Zhanlu vuole utilizzare il browser:", "consoleLogs": "Log della console", "noNewLogs": "(<PERSON><PERSON>un nuovo log)", "screenshot": "Screenshot del browser", "cursor": "cursore", "navigation": {"step": "Passo {{current}} di {{total}}", "previous": "Precedente", "next": "Successivo"}, "sessionStarted": "Sessione browser avviata", "actions": {"title": "Azione browser: ", "launch": "Avvia browser su {{url}}", "click": "Clic ({{coordinate}})", "type": "Digita \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON> verso il basso", "scrollUp": "<PERSON><PERSON><PERSON> verso l'alto", "close": "Chiudi browser"}}, "codeblock": {"tooltips": {"expand": "Espandi blocco di codice", "collapse": "Comprimi blocco di codice", "enable_wrap": "Attiva a capo automatico", "disable_wrap": "Disattiva a capo automatico", "copy_code": "Copia codice"}}, "qucikInstructions": {"UiToCode": "Codice di generazione di grafici UI", "UmlToCode": "Codice di generazione di grafici UML", "ExplainCode": "Interpretazione del codice", "FixCode": "Errore di codice", "ImproveCode": "Ottimizzazione del codice", "UnitTest": "Test unitari", "CODE_REVIEW": "Revisione del codice", "CommentCode": "Commento del codice", "PlusButtonClicked": "Svuota finestra di dialogo"}, "systemPromptWarning": "ATTENZIONE: Sovrascrittura personalizzata delle istruzioni di sistema attiva. Questo può compromettere gravemente le funzionalità e causare comportamenti imprevedibili.", "profileViolationWarning": "Il profilo corrente non è compatibile con le impostazioni della tua organizzazione", "shellIntegration": {"title": "Avviso di esecuzione comando", "description": "Il tuo comando viene eseguito senza l'integrazione shell del terminale VSCode. Per sopprimere questo avviso puoi disattivare l'integrazione shell nella sezione <strong>Terminal</strong> delle <settingsLink>impostazioni di Zhanlu</settingsLink> o risolvere i problemi di integrazione del terminale VSCode utilizzando il link qui sotto.", "troubleshooting": "Clicca qui per la documentazione sull'integrazione shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limite di Richieste Auto-approvate Raggiunto", "description": "zhan<PERSON> ha raggiunto il limite auto-approvato di {{count}} richiesta/e API. Vuoi reimpostare il contatore e procedere con l'attività?", "button": "Reimposta e Continua"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON><PERSON> vuole cercare nella base di codice <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> vuole cercare nella base di codice <code>{{query}}</code> in <code>{{path}}</code>:", "didSearch_one": "Trovato 1 risultato", "didSearch_other": "Trovati {{count}} risultati", "resultTooltip": "Punteggio di somiglianza: {{score}} (clicca per aprire il file)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON><PERSON> tutto"}, "deny": {"title": "<PERSON><PERSON>a tutto"}}, "indexingStatus": {"ready": "Indice pronto", "indexing": "Indicizzazione {{percentage}}%", "indexed": "Indicizzato", "error": "Errore indice", "status": "Stato indice"}, "versionIndicator": {"ariaLabel": "Versione {{version}} - <PERSON><PERSON><PERSON> per visualizzare le note di rilascio"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> Cloud arriva presto!", "description": "Esegui agenti remoti nel cloud, accedi alle tue attività da qualsiasi luogo, collabora con altri e molto altro.", "joinWaitlist": "Unisciti alla lista d'attesa per ottenere l'accesso anticipato."}, "editMessage": {"placeholder": "Modifica il tuo messaggio..."}}