{"title": "<PERSON><PERSON>", "done": "<PERSON><PERSON>", "modes": {"title": "Modalità", "createNewMode": "Crea nuova modalità", "importMode": "Importa modalità", "noMatchFound": "Nessuna modalità trovata", "editModesConfig": "Modifica configurazione modalità", "editGlobalModes": "Modifica modalità globali", "editProjectModes": "Modifica modalità di progetto (.z<PERSON><PERSON>)", "createModeHelpText": "Clicca sul + per creare una nuova modalità personalizzata, o chiedi semplicemente a Zhanlu nella chat di crearne una per te!", "selectMode": "Cerca modalità"}, "apiConfiguration": {"title": "Configurazione API", "select": "Seleziona quale configurazione API utilizzare per questa modalità"}, "tools": {"title": "Strumenti disponibili", "builtInModesText": "Gli strumenti per le modalità integrate non possono essere modificati", "editTools": "Modifica strumenti", "doneEditing": "Modifica completata", "allowedFiles": "File consentiti:", "toolNames": {"read": "Leggi file", "edit": "Modifica file", "browser": "Usa browser", "command": "<PERSON><PERSON><PERSON><PERSON> comandi", "mcp": "Usa MCP"}, "noTools": "<PERSON><PERSON><PERSON>"}, "roleDefinition": {"title": "Definizione del ruolo", "resetToDefault": "Ripristina predefiniti", "description": "Definisci l'esperienza e la personalità di zhanlu per questa modalità. Questa descrizione modella come zhanlu si presenta e affronta i compiti."}, "description": {"title": "Descrizione breve (per umani)", "resetToDefault": "Ripristina alla descrizione predefinita", "description": "Una breve descrizione mostrata nel menu a discesa del selettore di modalità."}, "whenToUse": {"title": "<PERSON>uan<PERSON> util<PERSON> (opzionale)", "description": "Descrivi quando questa modalità dovrebbe essere utilizzata. Questo aiuta l'Orchestrator a scegliere la modalità giusta per un compito.", "resetToDefault": "Ripristina la descrizione 'Quando utilizzare' ai valori predefiniti"}, "customInstructions": {"title": "Istruzioni personalizzate specifiche per la modalità (opzionale)", "resetToDefault": "Ripristina predefiniti", "description": "Aggiungi linee guida comportamentali specifiche per la modalità {{modeName}}.", "loadFromFile": "Le istruzioni personalizzate specifiche per la modalità {{mode}} possono essere caricate anche dalla cartella <span>.zhanlu/rules-{{slug}}/</span> nel tuo spazio di lavoro (.zhan<PERSON><PERSON><PERSON>-{{slug}} e .clinerules-{{slug}} sono obsoleti e smetteranno di funzionare presto)."}, "exportMode": {"title": "Esporta modalità", "description": "Esporta questa modalità in un file YAML con tutte le regole incluse per una facile condivisione con altri.", "export": "Esporta modalità", "exporting": "Esportazione..."}, "importMode": {"selectLevel": "Scegli dove importare questa modalità:", "import": "Importa", "importing": "Importazione...", "global": {"label": "Livello globale", "description": "Disponibile in tutti i progetti. Le regole verranno unite nelle istruzioni personalizzate."}, "project": {"label": "Livello di progetto", "description": "Di<PERSON>oni<PERSON>e solo in questo spazio di lavoro. Se la modalità esportata conteneva file di regole, verranno ricreati nella cartella .zhanlu/rules-{slug}/."}}, "advanced": {"title": "<PERSON><PERSON><PERSON>"}, "globalCustomInstructions": {"title": "Istruzioni personalizzate per tutte le modalità", "description": "Queste istruzioni si applicano a tutte le modalità. Forniscono un insieme base di comportamenti che possono essere migliorati dalle istruzioni specifiche per modalità qui sotto.\nSe desideri che Z<PERSON>lu pensi e parli in una lingua diversa dalla lingua di visualizzazione del tuo editor ({{language}}), puoi specificarlo qui.", "loadFromFile": "Le istruzioni possono essere caricate anche dalla cartella <span>.zhanlu/rules/</span> nel tuo spazio di lavoro."}, "systemPrompt": {"preview": "Anteprima prompt di sistema", "copy": "Copia prompt di sistema negli appunti", "title": "Prompt di sistema (modalità {{modeName}})"}, "supportPrompts": {"title": "Prompt di supporto", "resetPrompt": "Rip<PERSON>ina il prompt {{promptType}} ai valori predefiniti", "prompt": "Prompt", "enhance": {"apiConfiguration": "Configurazione API", "apiConfigDescription": "Puoi selezionare una configurazione API da usare sempre per migliorare i prompt, o semplicemente usare quella attualmente selezionata", "useCurrentConfig": "Usa la configurazione API attualmente selezionata", "testPromptPlaceholder": "Inserisci un prompt per testare il miglioramento", "previewButton": "Anteprima miglioramento prompt", "testEnhancement": "Testa miglioramento"}, "condense": {"apiConfiguration": "Configurazione API per la condensazione del contesto", "apiConfigDescription": "Seleziona quale configurazione API utilizzare per le operazioni di condensazione del contesto. Lascia deselezionato per utilizzare la configurazione attiva corrente.", "useCurrentConfig": "Usa la configurazione API currently selezionata"}, "types": {"ENHANCE": {"label": "<PERSON><PERSON><PERSON> prompt", "description": "Utilizza il miglioramento dei prompt per ottenere suggerimenti o miglioramenti personalizzati per i tuoi input. Questo assicura che zhanlu comprenda la tua intenzione e fornisca le migliori risposte possibili. Disponibile tramite l'icona ✨ nella chat."}, "CONDENSE": {"label": "Condensazione del contesto", "description": "Configura come viene condensato il contesto della conversazione per gestire i limiti dei token. Questo prompt viene utilizzato sia per le operazioni di condensazione del contesto manuali che automatiche."}, "EXPLAIN": {"label": "<PERSON><PERSON><PERSON> codice", "description": "Ottieni spiegazioni dettagliate di frammenti di codice, funzioni o file interi. Utile per comprendere codice complesso o imparare nuovi pattern. Disponibile nelle azioni di codice (icona della lampadina nell'editor) e nel menu contestuale dell'editor (clic destro sul codice selezionato)."}, "FIX": {"label": "<PERSON><PERSON><PERSON><PERSON> problemi", "description": "Ottieni aiuto per identificare e risolvere bug, errori o problemi di qualità del codice. Fornisce una guida passo-passo per risolvere i problemi. Disponibile nelle azioni di codice (icona della lampadina nell'editor) e nel menu contestuale dell'editor (clic destro sul codice selezionato)."}, "IMPROVE": {"label": "<PERSON><PERSON><PERSON> codice", "description": "<PERSON>vi suggerimenti per l'ottimizzazione del codice, migliori pratiche e miglioramenti architetturali mantenendo la funzionalità. Disponibile nelle azioni di codice (icona della lampadina nell'editor) e nel menu contestuale dell'editor (clic destro sul codice selezionato)."}, "ADD_TO_CONTEXT": {"label": "Aggiungi al contesto", "description": "Aggiungi contesto al tuo compito o conversazione attuale. Utile per fornire informazioni aggiuntive o chiarimenti. Disponibile nelle azioni di codice (icona della lampadina nell'editor) e nel menu contestuale dell'editor (clic destro sul codice selezionato)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Aggiungi contenuto del terminale al contesto", "description": "Aggiungi l'output del terminale al tuo compito o conversazione attuale. Utile per fornire output di comandi o log. Disponibile nel menu contestuale del terminale (clic destro sul contenuto selezionato del terminale)."}, "TERMINAL_FIX": {"label": "Correggi comando del terminale", "description": "Ottieni aiuto per correggere i comandi del terminale che hanno fallito o necessitano di miglioramenti. Disponibile nel menu contestuale del terminale (clic destro sul contenuto selezionato del terminale)."}, "TERMINAL_EXPLAIN": {"label": "Spiega comando del terminale", "description": "Ottieni spiegazioni dettagliate sui comandi del terminale e sui loro output. Disponibile nel menu contestuale del terminale (clic destro sul contenuto selezionato del terminale)."}, "NEW_TASK": {"label": "Avvia nuova attività", "description": "Avvia una nuova attività con il tuo input. Disponibile nella palette dei comandi."}, "UNIT_TEST": {"label": "Test unitario", "description": "Genera test unitari completi per il codice selezionato. Disponibile nelle azioni di codice (icona della lampadina nell'editor) e nel menu contestuale dell'editor (clic destro sul codice selezionato)."}, "CODE_REVIEW": {"label": "Revisione codice", "description": "Esegui una revisione dettagliata del codice e fornisci suggerimenti di miglioramento per il codice selezionato. Disponibile nelle azioni di codice (icona della lampadina nell'editor) e nel menu contestuale dell'editor (clic destro sul codice selezionato)."}, "COMMENT_CODE": {"label": "Commenta codice", "description": "Aggiungi commenti dettagliati e documentazione al codice selezionato. Disponibile nelle azioni di codice (icona della lampadina nell'editor) e nel menu contestuale dell'editor (clic destro sul codice selezionato)."}}}, "advancedSystemPrompt": {"title": "Avanzato: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prompt di sistema", "description": "Puoi sostituire completamente il prompt di sistema per questa modalità (a parte la definizione del ruolo e le istruzioni personalizzate) creando un file in <span>.zhanlu/system-prompt-{{slug}}</span> nel tuo spazio di lavoro. Questa è una funzionalità molto avanzata che bypassa le protezioni integrate e i controlli di coerenza (specialmente riguardo all'uso degli strumenti), quindi fai attenzione!"}, "createModeDialog": {"title": "Crea nuova modalità", "close": "<PERSON><PERSON>", "name": {"label": "Nome", "placeholder": "Inserisci nome modalità", "tooLong": "Il nome non può superare 20 caratteri"}, "slug": {"label": "Slug", "description": "Lo slug viene utilizzato negli URL e nei nomi dei file. Deve essere in minuscolo e contenere solo lettere, numeri e trattini.", "tooLong": "Lo slug non può superare 20 caratteri"}, "saveLocation": {"label": "Posizione di salvataggio", "description": "Scegli dove salvare questa modalità. Le modalità specifiche del progetto hanno la precedenza sulle modalità globali.", "global": {"label": "Globale", "description": "Disponibile in tutti gli spazi di lavoro"}, "project": {"label": "Specifico del progetto (.z<PERSON>)", "description": "Di<PERSON>oni<PERSON>e solo in questo spazio di lavoro, ha la precedenza sul globale"}}, "roleDefinition": {"label": "Definizione del ruolo", "description": "Definisci l'esperienza e la personalità di zhanlu per questa modalità."}, "description": {"label": "Descrizione breve (per umani)", "description": "Una breve descrizione mostrata nel menu a discesa del selettore di modalità."}, "whenToUse": {"label": "<PERSON>uan<PERSON> util<PERSON> (opzionale)", "description": "Fornisci una chiara descrizione di quando questa modalità è più efficace e per quali tipi di compiti eccelle."}, "tools": {"label": "Strumenti disponibili", "description": "Seleziona quali strumenti questa modalità può utilizzare."}, "customInstructions": {"label": "Istruzioni personalizzate (opzionale)", "description": "Aggiungi linee guida comportamentali specifiche per questa modalità."}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "create": "<PERSON>rea modalit<PERSON>"}, "deleteMode": "Elimina modalità"}, "allFiles": "tutti i file", "deleteMode": {"title": "Elimina modalità", "message": "Sei sicuro di voler eliminare la modalità \"{{modeName}}\"?", "rulesFolder": "Questa modalità ha una cartella di regole in {{folderPath}} che verrà eliminata.", "descriptionNoRules": "Sei sicuro di voler eliminare questa modalità personalizzata?", "confirm": "Elimina", "cancel": "<PERSON><PERSON><PERSON>"}}