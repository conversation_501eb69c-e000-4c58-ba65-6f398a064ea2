{"errorBoundary": {"title": "Qualcosa è andato storto", "reportText": "Aiutaci a migliorare segnalando questo errore su", "githubText": "la nostra pagina GitHub Issues", "copyInstructions": "Copia e incolla il seguente messaggio di errore per includerlo come parte della tua segnalazione:", "errorStack": "<PERSON>ack di errore:", "componentStack": "Stack dei componenti:"}, "answers": {"yes": "Sì", "no": "No", "cancel": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON>", "keep": "<PERSON><PERSON><PERSON>"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "Cerca..."}, "mermaid": {"loading": "Generazione del diagramma mermaid...", "render_error": "Impossibile renderizzare il diagramma", "file_media": "Anteprima del processo", "code": "Mostra codice sorgente", "buttons": {"zoom": "Zoom", "zoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copia", "save": "<PERSON>va immagine", "viewCode": "Visualizza codice", "viewDiagram": "Visualizza diagramma", "close": "<PERSON><PERSON>"}, "modal": {"codeTitle": "Codice Mermaid"}, "tabs": {"diagram": "Di<PERSON>ram<PERSON>", "code": "Codice"}, "feedback": {"imageCopied": "Immagine copiata negli appunti", "copyError": "Errore nella copia dell'immagine"}}, "file": {"errors": {"invalidDataUri": "Formato URI dati non valido", "copyingImage": "Errore nella copia dell'immagine: {{error}}", "openingImage": "Errore nell'apertura dell'immagine: {{error}}", "pathNotExists": "Il percorso non esiste: {{path}}", "couldNotOpen": "Impossibile aprire il file: {{error}}", "couldNotOpenGeneric": "Impossibile aprire il file!"}, "success": {"imageDataUriCopied": "URI dati immagine copiato negli appunti"}}, "confirmation": {"deleteMessage": "Elimina <PERSON>", "deleteWarning": "Eliminando questo messaggio verranno eliminati tutti i messaggi successivi nella conversazione. Vuoi procedere?", "editMessage": "Modifica Messaggio", "editWarning": "Modificando questo messaggio verranno eliminati tutti i messaggi successivi nella conversazione. Vuoi procedere?", "proceed": "<PERSON><PERSON><PERSON>"}}