{"errorBoundary": {"title": "Coś poszło nie tak", "reportText": "Pomóż nam ulepszyć aplikację, zgłaszając ten błąd na", "githubText": "<PERSON><PERSON><PERSON> s<PERSON>ie <PERSON>", "copyInstructions": "Skopiuj i wklej poniższy komunikat o błędzie, aby dołączyć go do zgłoszenia:", "errorStack": "Stos błędu:", "componentStack": "Stos komponentów:"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "answers": {"yes": "Tak", "no": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Usuń", "keep": "<PERSON><PERSON><PERSON>"}, "ui": {"search_placeholder": "Szukaj..."}, "mermaid": {"loading": "Generowanie diagramu mermaid...", "render_error": "<PERSON><PERSON> m<PERSON><PERSON>u", "file_media": "Podgląd procesu", "code": "Pokaż kod źródłowy", "buttons": {"zoom": "Powiększenie", "zoomIn": "Powię<PERSON><PERSON>", "zoomOut": "Po<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON><PERSON>", "save": "Zapisz obraz", "viewCode": "Zobacz kod", "viewDiagram": "<PERSON><PERSON><PERSON><PERSON> diagram", "close": "Zamknij"}, "modal": {"codeTitle": "Kod Mermaid"}, "tabs": {"diagram": "Diagram", "code": "Kod"}, "feedback": {"imageCopied": "Obraz skopiowany do schowka", "copyError": "Błąd kopiowania obrazu"}}, "file": {"errors": {"invalidDataUri": "Nieprawidłowy format URI danych", "copyingImage": "Błąd kopiowania obrazu: {{error}}", "openingImage": "Błąd otwierania obrazu: {{error}}", "pathNotExists": "Ścieżka nie istnieje: {{path}}", "couldNotOpen": "<PERSON>e można otworzyć pliku: {{error}}", "couldNotOpenGeneric": "Nie można otworzyć pliku!"}, "success": {"imageDataUriCopied": "URI danych obrazu skopiowane do schowka"}}, "confirmation": {"deleteMessage": "<PERSON><PERSON><PERSON>", "deleteWarning": "Usunięcie tej wiadomości spowoduje usunięcie wszystkich kolejnych wiadomości w rozmowie. <PERSON><PERSON> ch<PERSON> kontynuowa<PERSON>?", "editMessage": "<PERSON><PERSON><PERSON><PERSON>", "editWarning": "Edycja tej wiadomości spowoduje usunięcie wszystkich kolejnych wiadomości w rozmowie. <PERSON><PERSON> ch<PERSON> kont<PERSON>uowa<PERSON>?", "proceed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}