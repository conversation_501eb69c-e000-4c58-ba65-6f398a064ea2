{"greeting": "Witaj w zhan<PERSON>!", "introduction": "Dzięki szerokiej gamie wbudowanych i rozszerzalnych Trybów, <PERSON><PERSON><PERSON> pozwala planować, projektować architekturę, k<PERSON><PERSON><PERSON>, debugować i zwiększać produktywność jak nigdy dotąd.", "notice": "<PERSON><PERSON>, to rozszerzenie potrzebuje dostawcy API.", "start": "Zaczynajmy!", "routers": {"requesty": {"description": "Twój zoptymalizowany router LLM", "incentive": "$1 darmowego kredytu"}, "openrouter": {"description": "Ujednolicony interfejs dla LLMs"}}, "chooseProvider": "<PERSON><PERSON>, zhanlu potrzebuje klucza API.", "startRouter": "Zalecamy korzystanie z routera LLM:", "startCustom": "Lub możesz użyć własnego klucza API:", "telemetry": {"title": "<PERSON><PERSON><PERSON><PERSON>", "anonymousTelemetry": "Wyślij anonimowe dane o błędach i użyciu, aby pomóc nam w naprawianiu błędów i ulepszaniu rozszerzenia. Nigdy nie są wysyłane żadne kody, teksty ani informacje osobiste.", "changeSettings": "Zawsze możesz to zmienić na dole <settingsLink>ustawień</settingsLink>", "settings": "ustawienia", "allow": "Zezwól", "deny": "Odmów"}, "importSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}