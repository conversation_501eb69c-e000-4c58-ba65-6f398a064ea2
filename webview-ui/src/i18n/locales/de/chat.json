{"greeting": "Willkommen bei Zhanlu", "task": {"title": "Aufgabe", "seeMore": "<PERSON><PERSON> anzeigen", "seeLess": "<PERSON><PERSON> anzeigen", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API-Kosten:", "contextWindow": "Kontextfenster:", "closeAndStart": "Aufgabe schließen und neue starten", "export": "Aufgabenverlauf exportieren", "delete": "Aufgabe löschen (Shift + Klick zum Überspringen der Bestätigung)", "share": "Aufgabe teilen", "condenseContext": "Kontext intelligent komprimieren", "shareWithOrganization": "Mit Organisation teilen", "shareWithOrganizationDescription": "Nur Mitglieder deiner Organisation können zugreifen", "sharePublicly": "<PERSON><PERSON><PERSON>lich teilen", "sharePubliclyDescription": "<PERSON><PERSON> mit dem Link kann zugreifen", "connectToCloud": "<PERSON><PERSON> <PERSON> verbinden", "connectToCloudDescription": "Melde dich bei zhanlu Cloud an, um Aufgaben zu teilen", "sharingDisabledByOrganization": "Freigabe von der Organisation deaktiviert", "shareSuccessOrganization": "Organisationslink in die Zwischenablage kopiert", "shareSuccessPublic": "Öffentlicher Link in die Zwischenablage kopiert"}, "unpin": "<PERSON><PERSON><PERSON>", "pin": "Anheften", "tokenProgress": {"availableSpace": "Verfügbarer S<PERSON>icher: {{amount}} Tokens", "tokensUsed": "Verwen<PERSON><PERSON> Tokens: {{used}} von {{total}}", "reservedForResponse": "Reserviert für Modellantwort: {{amount}} Tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": "Versuch erneut starten"}, "startNewTask": {"title": "Neue Aufgabe starten", "tooltip": "<PERSON><PERSON>ne eine neue Aufgabe"}, "proceedAnyways": {"title": "Trotzdem fortfahren", "tooltip": "Während der Befehlsausführung fortfahren"}, "save": {"title": "Speichern", "tooltip": "Nachrichtenänderungen speichern"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Diese Aktion ablehnen"}, "completeSubtaskAndReturn": "Teilaufgabe abschließen und zurückkehren", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Diese Aktion genehmigen"}, "runCommand": {"title": "Befehl ausführen", "tooltip": "<PERSON>sen Befehl ausführen"}, "proceedWhileRunning": {"title": "Während Ausführung fortfahren", "tooltip": "Trotz Warnungen fortfahren"}, "killCommand": {"title": "<PERSON><PERSON><PERSON> abbrechen", "tooltip": "Aktuellen Befehl abbrechen"}, "resumeTask": {"title": "Aufgabe fortsetzen", "tooltip": "Aktuelle Aufgabe fortsetzen"}, "terminate": {"title": "<PERSON>den", "tooltip": "Aktuelle Aufgabe beenden"}, "cancel": {"title": "Abbrechen", "tooltip": "Aktuelle Operation abbrechen"}, "scrollToBottom": "Zum Ende des Chats scrollen", "about": "<PERSON><PERSON><PERSON>, kor<PERSON><PERSON><PERSON>, refaktorisiere und debugge Code mit Hilfe des Zhanlu KI-Modells.<br /><PERSON><PERSON><PERSON> in unserer <DocsLink>Dokumentation</DocsLink> für mehr Informationen nach.", "onboarding": "Die Aufgabenliste in diesem Workspace ist leer. Beginne, indem du deine Aufgabe unten eingibst.<br><PERSON><PERSON> sicher, wie du anfangen sollst? Lies mehr in unserer <DocsLink>Dokumentation</DocsLink>.", "zhanluTips": {"architectMode": {"title": "Architekt-Modus", "description": "Erstellt detaillierte Pläne zur Implementierung von Lösungen"}, "codeMode": {"title": "Code-Modus", "description": "Schreibt und modifiziert Code nach bewährten Methoden"}, "testMode": {"title": "Unit-Test-Modus", "description": "Generiert umfassende Tests für deinen Code"}, "projectFixMode": {"title": "Projektfehlerbehebung-Modus", "description": "Identifiziert und behebt Defekte in Projekten"}, "sastMode": {"title": "Sicherheitskorrektur-Modus", "description": "Behebt Sicherheitslücken im Code"}, "codeReviewMode": {"title": "Code-Review-Modus", "description": "Bewertet Codequalität und schlägt Verbesserungen vor"}, "readmeMode": {"title": "Dokumentations-Modus", "description": "Erstellt detaillierte technische Dokumentation"}, "simpleMode": {"title": "Frage-Antwort-Modus", "description": "Beantwortet technische Fragen präzise"}, "boomerangTasks": {"title": "Aufgaben-Orchestrierung", "description": "<PERSON><PERSON> Aufgaben in kleinere, überschaubare Teile auf."}, "stickyModels": {"title": "<PERSON><PERSON>", "description": "Jeder Modus merkt sich dein zuletzt verwendetes Modell"}, "tools": {"title": "Tools", "description": "Erlaube der KI, Probleme durch Surfen im Web, Ausführen von <PERSON>en und mehr zu lösen."}, "customizableModes": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Spezialisierte Personas mit eigenem Verhalten und zugewiesenen Modellen"}}, "selectMode": "Interaktionsmodus auswählen", "selectApiConfig": "API-Konfiguration auswählen", "internetSearch": "<PERSON>n Sie die Internetsuche öffnen, können Sie im Internet nach relevanten Inhalten suchen", "internetSearchClosed": "Internetsuche schließen", "enhancePrompt": "Prompt mit zusätzlichem Kontext verbessern", "addImages": "Bilder zur Nachricht hinzufügen", "sendMessage": "Nachricht senden", "stopTts": "Text-in-<PERSON><PERSON><PERSON> beenden", "typeMessage": "Nachricht eingeben...", "typeTask": "Gib deine Aufgabe hier ein...", "addContext": "@ Hinzufü<PERSON> von Kontext, / Schaltmodus, # Schnellbefehle", "dragFiles": "Shift gedr<PERSON>t halten und Dateien ziehen", "dragFilesImages": "Shift gedr<PERSON>t halten und Dateien/Bilder ziehen", "enhancePromptDescription": "Die Schaltfläche 'Prompt verbessern' hi<PERSON><PERSON>, deine Anfrage durch zusätzlichen Kontext, Klarstellungen oder Umformulierungen zu verbessern. <PERSON><PERSON><PERSON>, hier eine Anfrage einzugeben und klicke erneut auf die Schaltfläche, um zu sehen, wie es funktioniert.", "modeSelector": {"title": "<PERSON><PERSON>", "marketplace": "Modus-Marketplace", "settings": "Modus-Einstellungen", "description": "Spezialis<PERSON><PERSON>, die Roos Verhalten anpassen."}, "errorReadingFile": "<PERSON><PERSON> beim Lesen der Datei:", "noValidImages": "<PERSON><PERSON> gültigen Bilder wurden verarbeitet", "separator": "Trennlinie", "edit": "Bearbeiten...", "forNextMode": "für nächsten Modus", "forPreviousMode": "für vorherigen Modus", "error": "<PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON>", "diffError": {"title": "Bearbeitung fehlgeschlagen"}, "troubleMessage": "<PERSON><PERSON><PERSON> hat Probleme...", "apiRequest": {"title": "API-Anfrage", "failed": "API-Anfrage fehlgeschlagen", "streaming": "API-Anfrage...", "cancelled": "API-Anfrage abgebrochen", "streamingFailed": "API-Streaming fehlgeschlagen"}, "checkpoint": {"initial": "Initialer Checkpoint", "regular": "Checkpoint", "initializingWarning": "Checkpoint wird noch initialisiert... <PERSON> dies zu lange dauert, kannst du Checkpoints in den <settingsLink>Einstellungen</settingsLink> deaktivieren und deine Aufgabe neu starten.", "menu": {"viewDiff": "Unterschiede anzeigen", "restore": "Checkpoint wiederherstellen", "restoreFiles": "<PERSON><PERSON> wied<PERSON>", "restoreFilesDescription": "Stellt die Dateien deines Projekts auf einen Snapshot zurück, der an diesem Punkt erstellt wurde.", "restoreFilesAndTask": "Dateien & Aufgabe wiederherstellen", "confirm": "Bestätigen", "cancel": "Abbrechen", "cannotUndo": "Diese Aktion kann nicht rückgängig gemacht werden.", "restoreFilesAndTaskDescription": "Stellt die Dateien deines Projekts auf einen Snapshot zurück, der an diesem Punkt erstellt wurde, und löscht alle Nachrichten nach diesem Punkt."}, "current": "Aktuell"}, "instructions": {"wantsToFetch": "<PERSON><PERSON>lu möchte detaillierte Anweisungen abrufen, um bei der aktuellen Aufgabe zu helfen"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON><PERSON> möchte diese Datei lesen:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> möchte diese Datei und {{count}} weitere lesen:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON><PERSON> möchte diese Datei außerhalb des Arbeitsbereichs lesen:", "didRead": "<PERSON><PERSON><PERSON> hat diese <PERSON><PERSON> gelesen:", "wantsToEdit": "<PERSON><PERSON><PERSON> möchte diese Datei bearbeiten:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON><PERSON> möchte diese Datei außerhalb des Arbeitsbereichs bearbeiten:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> möchte eine geschützte Konfigurationsdatei bearbeiten:", "wantsToCreate": "<PERSON><PERSON><PERSON> möchte eine neue Datei erstellen:", "wantsToSearchReplace": "<PERSON><PERSON><PERSON> möchte in dieser Datei suchen und ersetzen:", "didSearchReplace": "<PERSON><PERSON><PERSON> hat Suchen und Ersetzen in dieser Datei durchgeführt:", "wantsToInsert": "<PERSON><PERSON><PERSON> möchte Inhalte in diese Datei einfügen:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> möchte Inhalte in diese Datei in Zeile {{lineNumber}} einfügen:", "wantsToInsertAtEnd": "<PERSON><PERSON><PERSON> möchte Inhalte am Ende dieser Datei anhängen:", "wantsToReadMultiple": "<PERSON><PERSON><PERSON> möchte mehrere Dateien lesen:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON><PERSON> möchte die Dateien auf oberster Ebene in diesem Verzeichnis anzeigen:", "didViewTopLevel": "<PERSON><PERSON><PERSON> hat die Dateien auf oberster Ebene in diesem Verzeichnis angezeigt:", "wantsToViewRecursive": "<PERSON><PERSON><PERSON> möchte rekursiv alle Dateien in diesem Verzeichnis anzeigen:", "didViewRecursive": "<PERSON><PERSON><PERSON> hat rekursiv alle Dateien in diesem Verzeichnis angezeigt:", "wantsToViewDefinitions": "Zhanlu möchte Quellcode-Definitionsnamen in diesem Verzeichnis anzeigen:", "didViewDefinitions": "<PERSON><PERSON><PERSON> hat Quellcode-Definitionsnamen in diesem Verzeichnis angezeigt:", "wantsToSearch": "<PERSON><PERSON><PERSON> möchte dieses Verzeichnis nach <code>{{regex}}</code> durchsuchen:", "didSearch": "<PERSON><PERSON><PERSON> hat dieses Verzeichnis nach <code>{{regex}}</code> durchsucht:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> möchte dieses Verzeichnis (außerhalb des Arbeitsbereichs) nach <code>{{regex}}</code> durchsuchen:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> hat dieses Verzeichnis (außerhalb des Arbeitsbereichs) nach <code>{{regex}}</code> durchsucht:", "wantsToViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> möchte die Dateien auf oberster Ebene in diesem Verzeichnis (außerhalb des Arbeitsbereichs) anzeigen:", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> hat die Dateien auf oberster Ebene in diesem Verzeichnis (außerhalb des Arbeitsbereichs) angezeigt:", "wantsToViewRecursiveOutsideWorkspace": "<PERSON><PERSON>lu möchte rekursiv alle Dateien in diesem Verzeichnis (außerhalb des Arbeitsbereichs) anzeigen:", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON>lu hat rekursiv alle Dateien in diesem Verzeichnis (außerhalb des Arbeitsbereichs) angezeigt:", "wantsToViewDefinitionsOutsideWorkspace": "Zhanlu möchte Quellcode-Definitionsnamen in diesem Verzeichnis (außerhalb des Arbeitsbereichs) anzeigen:", "didViewDefinitionsOutsideWorkspace": "Zhanlu hat Quellcode-Definitionsnamen in diesem Verzeichnis (außerhalb des Arbeitsbereichs) angezeigt:"}, "commandOutput": "Befehlsausgabe", "commandExecution": {"running": "Wird ausgeführt", "pid": "PID: {{pid}}", "exited": "Beendet ({{exitCode}})", "manageCommands": "Befehlsberechtigungen verwalten", "commandManagementDescription": "Befehlsberechtigungen verwalten: Klicke auf ✓, um die automatische Ausführung zu erlauben, ✗, um die Ausführung zu verweigern. Muster können ein-/ausgeschaltet oder aus Listen entfernt werden. <settingsLink>Alle Einstellungen anzeigen</settingsLink>", "addToAllowed": "Zur Liste der erlaubten Befehle hinzufügen", "removeFromAllowed": "Von der Liste der erlaubten Befehle entfernen", "addToDenied": "Zur Liste der verweigerten Befehle hinzufügen", "removeFromDenied": "Von der Liste der verweigerten Befehle entfernen", "abortCommand": "Befehlsausführung abbrechen", "expandOutput": "Ausgabe erweitern", "collapseOutput": "Ausgabe einklappen", "expandManagement": "Befehlsverwaltungsbereich erweitern", "collapseManagement": "Befehlsverwaltungsbereich einklappen"}, "response": "Antwort", "arguments": "Argumente", "mcp": {"wantsToUseTool": "<PERSON><PERSON><PERSON> möchte ein Tool auf dem {{serverName}} MCP-Server verwenden:", "wantsToAccessResource": "<PERSON><PERSON><PERSON> möchte auf eine Ressource auf dem {{serverName}} MCP-Server zugreifen:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> möchte zum <code>{{mode}}</code>-<PERSON><PERSON> wechseln", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> möchte zum <code>{{mode}}</code>-<PERSON><PERSON> wechseln, weil: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> hat zum <code>{{mode}}</code>-Modus gewechselt", "didSwitchWithReason": "<PERSON><PERSON><PERSON> hat zum <code>{{mode}}</code>-<PERSON><PERSON> gewechselt, weil: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> möchte eine neue Teilaufgabe im <code>{{mode}}</code>-Modus erstellen:", "wantsToFinish": "<PERSON><PERSON><PERSON> möchte diese Teilaufgabe abschließen", "newTaskContent": "Teilaufgabenanweisungen", "completionContent": "Teilaufgabe abgeschlossen", "resultContent": "Teilaufgabenergebnisse", "defaultResult": "Bitte fahre mit der nächsten Aufgabe fort.", "completionInstructions": "Teilaufgabe abgeschlossen! Du kannst die Ergebnisse überprüfen und Korrekturen oder nächste Schritte vorschlagen. Wenn alles gut aussieht, bestätige, um das Ergebnis an die übergeordnete Aufgabe zurückzugeben."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> hat eine Frage:"}, "taskCompleted": "Aufgabe abgeschlossen", "powershell": {"issues": "<PERSON><PERSON> sche<PERSON>, dass du Probleme mit Windows PowerShell hast, bitte sieh dir dies an"}, "autoApprove": {"title": "Automatische Genehmigung:", "none": "<PERSON><PERSON>", "description": "Automatische Genehmigung erlaubt zhanlu, Aktionen ohne Nachfrage auszuführen. Aktiviere dies nur für Aktionen, denen du vollständig vertraust. Detailliertere Konfiguration verfügbar in den <settingsLink>Einstellungen</settingsLink>.", "selectOptionsFirst": "W<PERSON>hle mindestens eine der folgenden Optionen aus, um die automatische Genehmigung zu aktivieren", "toggleAriaLabel": "Automatische Genehmigung umschalten", "disabledAriaLabel": "Automatische Genehmigung deaktiviert - zuerst Optionen auswählen"}, "reasoning": {"thinking": "<PERSON><PERSON> nach", "seconds": "{{count}}s"}, "contextCondense": {"title": "Kontext komprimiert", "condensing": "Kontext wird komprimiert...", "errorHeader": "Kontext konnte nicht komprimiert werden", "tokens": "Tokens"}, "followUpSuggest": {"copyToInput": "In Eingabefeld kopieren (oder Shift + Klick)", "autoSelectCountdown": "Automatische Auswahl in {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Zhanlu 2.3.2 Versionsupdate", "description": "Fehlerbehebungen, Codevervollständigung", "whatsNew": "Wichtige Updates", "feature1": "<bold>Code-Foundation-Modus hinzugefügt</bold>: Code-Foundation-Modus generiert Programmierübungen", "feature2": "<bold>Historie-Aufgaben-Bug-Fix</bold>: <PERSON> behoben, bei dem neue Aufgaben in Historie-Aufgaben angezeigt werden", "feature3": "<bold>Flimmern-Problem-Fix</bold>: Gelegentliches Bildschirmflimmern beim Anzeigen von Code behoben", "feature4": "<bold>Andere Optimierungen</bold>: Verschiedene andere Problemoptimierungen", "hideButton": "Ankündigung ausblenden", "detailsDiscussLinks": "<PERSON><PERSON><PERSON> <discordLink>detaillierte Dokumentation</discordLink> für weitere Funktionen 🚀"}, "browser": {"rooWantsToUse": "<PERSON><PERSON><PERSON> möchte den Browser verwenden:", "consoleLogs": "Konsolenprotokolle", "noNewLogs": "(<PERSON><PERSON>)", "screenshot": "Browser-Screenshot", "cursor": "<PERSON><PERSON><PERSON>", "navigation": {"step": "Schritt {{current}} von {{total}}", "previous": "Zurück", "next": "<PERSON><PERSON>"}, "sessionStarted": "Browser-Sitzung gestartet", "actions": {"title": "Browser-Aktion: ", "launch": "Browser starten auf {{url}}", "click": "Klicken ({{coordinate}})", "type": "Eingeben \"{{text}}\"", "scrollDown": "Nach unten scrollen", "scrollUp": "Nach oben scrollen", "close": "<PERSON><PERSON><PERSON> schließen"}}, "codeblock": {"tooltips": {"expand": "Code-Block erweitern", "collapse": "Code-Block reduzieren", "enable_wrap": "Zeilenumbruch aktivieren", "disable_wrap": "Zeilenumbruch deaktivieren", "copy_code": "Code kopieren"}}, "qucikInstructions": {"UiToCode": "UI-Design-Code generieren", "UmlToCode": "UML-Diagramm-Code generieren", "ExplainCode": "Code-Interpretation", "FixCode": "Code Fehlerbehebung", "ImproveCode": "Code-Optimierung", "UnitTest": "Einheitsprüfung", "CODE_REVIEW": "Code-Bewertung", "CommentCode": "Code kommentieren", "PlusButtonClicked": "<PERSON><PERSON> leeren"}, "systemPromptWarning": "WARNUNG: Benutzerdefinierte Systemaufforderung aktiv. Dies kann die Funktionalität erheblich beeinträchtigen und zu unvorhersehbarem Verhalten führen.", "profileViolationWarning": "Das aktuelle Profil ist nicht kompatibel mit den Einstellungen deiner Organisation", "shellIntegration": {"title": "Befehlsausführungswarnung", "description": "Dein Be<PERSON>hl wird ohne VSCode Terminal-Shell-Integration ausgeführt. Um diese Warnung zu unterdrücken, kannst du die Shell-Integration im Abschnitt <strong>Terminal</strong> der <settingsLink>Zhanlu Einstellungen</settingsLink> deaktivieren oder die VSCode Terminal-Integration mit dem Link unten beheben.", "troubleshooting": "<PERSON><PERSON>e hier für die Shell-Integrationsdokumentation."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limit für automatisch genehmigte Anfragen erreicht", "description": "zhan<PERSON> hat das automatisch genehmigte Limit von {{count}} API-Anfrage(n) erreicht. Möchtest du den Zähler zurücksetzen und mit der Aufgabe fortfahren?", "button": "Zurücksetzen und fortfahren"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON><PERSON> möchte den Codebase nach <code>{{query}}</code> durchsuchen:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> m<PERSON>chte den Codebase nach <code>{{query}}</code> in <code>{{path}}</code> durchsuchen:", "didSearch_one": "1 Ergebnis gefunden", "didSearch_other": "{{count}} Ergebnisse gefunden", "resultTooltip": "Ähnlichkeitswert: {{score}} (klicken zum Öffnen der Datei)"}, "read-batch": {"approve": {"title": "Alle genehmigen"}, "deny": {"title": "<PERSON>e <PERSON>en"}}, "indexingStatus": {"ready": "Index bereit", "indexing": "Indizierung {{percentage}}%", "indexed": "Indiziert", "error": "Index-<PERSON><PERSON>", "status": "Index-Status"}, "versionIndicator": {"ariaLabel": "Version {{version}} - <PERSON><PERSON><PERSON>, um die Versionshinweise anzuzeigen"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> <PERSON> kommt bald!", "description": "Führe Remote-Agenten in der Cloud aus, greife von überall auf deine Aufgaben zu, arbeite mit anderen zusammen und vieles mehr.", "joinWaitlist": "Tritt der Warteliste bei, um frühen Zugang zu erhalten."}, "editMessage": {"placeholder": "Bearbeite deine Nachricht..."}}