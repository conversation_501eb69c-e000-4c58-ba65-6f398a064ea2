{"greeting": "Zhanlu へようこそ", "task": {"title": "タスク", "seeMore": "もっと見る", "seeLess": "表示を減らす", "tokens": "トークン:", "cache": "キャッシュ:", "apiCost": "APIコスト:", "contextWindow": "コンテキストウィンドウ:", "closeAndStart": "タスクを閉じて新しいタスクを開始", "export": "タスク履歴をエクスポート", "delete": "タスクを削除（Shift + クリックで確認をスキップ）", "condenseContext": "コンテキストをインテリジェントに圧縮", "share": "タスクを共有", "shareWithOrganization": "組織と共有", "shareWithOrganizationDescription": "組織のメンバーのみがアクセスできます", "sharePublicly": "公開で共有", "sharePubliclyDescription": "リンクを持つ誰でもアクセスできます", "connectToCloud": "クラウドに接続", "connectToCloudDescription": "タスクを共有するためにzhanlu Cloudにサインイン", "sharingDisabledByOrganization": "組織により共有が無効化されています", "shareSuccessOrganization": "組織リンクをクリップボードにコピーしました", "shareSuccessPublic": "公開リンクをクリップボードにコピーしました"}, "unpin": "ピン留めを解除", "pin": "ピン留め", "tokenProgress": {"availableSpace": "利用可能な空き容量: {{amount}} トークン", "tokensUsed": "使用トークン: {{used}} / {{total}}", "reservedForResponse": "モデル応答用に予約: {{amount}} トークン"}, "retry": {"title": "再試行", "tooltip": "操作を再試行"}, "startNewTask": {"title": "新しいタスクを開始", "tooltip": "新しいタスクを開始"}, "proceedAnyways": {"title": "それでも続行", "tooltip": "コマンド実行中でも続行"}, "save": {"title": "保存", "tooltip": "メッセージの変更を保存"}, "reject": {"title": "拒否", "tooltip": "このアクションを拒否"}, "completeSubtaskAndReturn": "サブタスクを完了して戻る", "approve": {"title": "承認", "tooltip": "このアクションを承認"}, "runCommand": {"title": "コマンド実行", "tooltip": "このコマンドを実行"}, "proceedWhileRunning": {"title": "実行中も続行", "tooltip": "警告にもかかわらず続行"}, "killCommand": {"title": "コマンドを強制終了", "tooltip": "現在のコマンドを強制終了します"}, "resumeTask": {"title": "タスクを再開", "tooltip": "現在のタスクを続行"}, "terminate": {"title": "終了", "tooltip": "現在のタスクを終了"}, "cancel": {"title": "キャンセル", "tooltip": "現在の操作をキャンセル"}, "scrollToBottom": "チャットの最下部にスクロール", "about": "Zhanlu AIモデルを使用してコードの生成、修正、リファクタリング、デバッグを行います。<br />詳細については<DocsLink>ドキュメント</DocsLink>をご覧ください。", "onboarding": "このワークスペースのタスクリストは空です。下記にタスクを入力して開始してください。<br>始め方が分からない場合は、<DocsLink>ドキュメント</DocsLink>で詳細をご確認ください。", "zhanluTips": {"architectMode": {"title": "アーキテクトモード", "description": "ソリューション実装の詳細計画を作成します"}, "codeMode": {"title": "コーディングモード", "description": "ベストプラクティスに従ってコードを作成・修正します"}, "testMode": {"title": "単体テストモード", "description": "コードの包括的なテストを生成します"}, "projectFixMode": {"title": "プロジェクト修正モード", "description": "プロジェクトの欠陥を特定して修正します"}, "sastMode": {"title": "セキュリティ修正モード", "description": "コードのセキュリティ脆弱性を修正します"}, "codeReviewMode": {"title": "コードレビューモード", "description": "コード品質を評価し改善を提案します"}, "readmeMode": {"title": "ドキュメントモード", "description": "詳細な技術ドキュメントを作成します"}, "simpleMode": {"title": "Q&Aモード", "description": "技術的な質問に正確に回答します"}, "boomerangTasks": {"title": "タスクオーケストレーション", "description": "タスクをより小さく、管理しやすい部分に分割します。"}, "stickyModels": {"title": "スティッキーモード", "description": "各モードは、最後に使用したモデルを記憶しています"}, "tools": {"title": "ツール", "description": "AIがWebの閲覧、コマンドの実行などによって問題を解決できるようにします。"}, "customizableModes": {"title": "カスタマイズ可能なモード", "description": "独自の動作と割り当てられたモデルを持つ専門的なペルソナ"}}, "selectMode": "対話モードを選択", "selectApiConfig": "API構成を選択", "internetSearch": "インターネット検索をオンにすると、インターネット上の関連コンテンツを検索できます", "internetSearchClosed": "インターネット検索の停止", "enhancePrompt": "追加コンテキストでプロンプトを強化", "addImages": "メッセージに画像を追加", "sendMessage": "メッセージを送信", "stopTts": "テキスト読み上げを停止", "typeMessage": "メッセージを入力...", "typeTask": "ここにタスクを入力...", "addContext": "@コンテキストの追加、/モードの切り替え、#ショートカット命令", "dragFiles": "Shiftキーを押しながらファイルをドラッグ", "dragFilesImages": "Shiftキーを押しながらファイル/画像をドラッグ", "enhancePromptDescription": "「プロンプトを強化」ボタンは、追加コンテキスト、説明、または言い換えを提供することで、リクエストを改善します。ここにリクエストを入力し、ボタンを再度クリックして動作を確認してください。", "modeSelector": {"title": "モード", "marketplace": "モードマーケットプレイス", "settings": "モード設定", "description": "Rooの動作をカスタマイズする専門的なペルソナ。"}, "errorReadingFile": "ファイル読み込みエラー:", "noValidImages": "有効な画像が処理されませんでした", "separator": "区切り", "edit": "編集...", "forNextMode": "次のモード用", "forPreviousMode": "前のモード用", "error": "エラー", "warning": "警告", "diffError": {"title": "編集に失敗しました"}, "troubleMessage": "<PERSON><PERSON><PERSON>に問題が発生しています...", "apiRequest": {"title": "APIリクエスト", "failed": "APIリクエスト失敗", "streaming": "APIリクエスト...", "cancelled": "APIリクエストキャンセル", "streamingFailed": "APIストリーミング失敗"}, "checkpoint": {"initial": "初期チェックポイント", "regular": "チェックポイント", "initializingWarning": "チェックポイントの初期化中... 時間がかかりすぎる場合は、<settingsLink>設定</settingsLink>でチェックポイントを無効にしてタスクを再開できます。", "menu": {"viewDiff": "差分を表示", "restore": "チェックポイントを復元", "restoreFiles": "ファイルを復元", "restoreFilesDescription": "この時点で撮影されたスナップショットにプロジェクトのファイルを復元します。", "restoreFilesAndTask": "ファイルとタスクを復元", "confirm": "確認", "cancel": "キャンセル", "cannotUndo": "このアクションは元に戻せません。", "restoreFilesAndTaskDescription": "この時点で撮影されたスナップショットにプロジェクトのファイルを復元し、この時点以降のすべてのメッセージを削除します。"}, "current": "現在"}, "instructions": {"wantsToFetch": "<PERSON><PERSON><PERSON>は現在のタスクを支援するための詳細な指示を取得したい"}, "fileOperations": {"wantsToRead": "Zhanluはこのファイルを読みたい:", "wantsToReadOutsideWorkspace": "Zhanluはワークスペース外のこのファイルを読みたい:", "didRead": "Zhanluはこのファイルを読みました:", "wantsToEdit": "Zhanluはこのファイルを編集したい:", "wantsToEditOutsideWorkspace": "Zhanluはワークスペース外のこのファイルを編集したい:", "wantsToEditProtected": "<PERSON><PERSON><PERSON>は保護された設定ファイルを編集したい:", "wantsToCreate": "<PERSON><PERSON><PERSON>は新しいファイルを作成したい:", "wantsToSearchReplace": "Zhanluはこのファイルで検索と置換を行う:", "didSearchReplace": "Zhanluはこのファイルで検索と置換を実行しました:", "wantsToInsert": "Zhanluはこのファイルにコンテンツを挿入したい:", "wantsToInsertWithLineNumber": "Zhanluはこのファイルの{{lineNumber}}行目にコンテンツを挿入したい:", "wantsToInsertAtEnd": "Zhanluはこのファイルの末尾にコンテンツを追加したい:", "wantsToReadAndXMore": "<PERSON>hanlu はこのファイルと他に {{count}} 個のファイルを読み込もうとしています:", "wantsToReadMultiple": "<PERSON><PERSON>luは複数のファイルを読み取ろうとしています：", "wantsToApplyBatchChanges": "<PERSON><PERSON><PERSON>は複数のファイルに変更を適用したい:"}, "directoryOperations": {"wantsToViewTopLevel": "Zhanluはこのディレクトリのトップレベルファイルを表示したい:", "didViewTopLevel": "Zhanluはこのディレクトリのトップレベルファイルを表示しました:", "wantsToViewRecursive": "Zhanluはこのディレクトリのすべてのファイルを再帰的に表示したい:", "didViewRecursive": "Zhanluはこのディレクトリのすべてのファイルを再帰的に表示しました:", "wantsToViewDefinitions": "Zhanluはこのディレクトリで使用されているソースコード定義名を表示したい:", "didViewDefinitions": "Zhanluはこのディレクトリで使用されているソースコード定義名を表示しました:", "wantsToSearch": "Zhanluはこのディレクトリで <code>{{regex}}</code> を検索したい:", "didSearch": "Zhanluはこのディレクトリで <code>{{regex}}</code> を検索しました:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON><PERSON>このディレクトリ（ワークスペース外）で <code>{{regex}}</code> を検索したい:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON><PERSON>このディレクトリ（ワークスペース外）で <code>{{regex}}</code> を検索しました:", "wantsToViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON><PERSON>このディレクトリ（ワークスペース外）のトップレベルファイルを表示したい:", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON>はこのディレクトリ（ワークスペース外）のトップレベルファイルを表示しました:", "wantsToViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON>はこのディレクトリ（ワークスペース外）のすべてのファイルを再帰的に表示したい:", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON>はこのディレクトリ（ワークスペース外）のすべてのファイルを再帰的に表示しました:", "wantsToViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON>はこのディレクトリ（ワークスペース外）で使用されているソースコード定義名を表示したい:", "didViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON>はこのディレクトリ（ワークスペース外）で使用されているソースコード定義名を表示しました:"}, "commandOutput": "コマンド出力", "commandExecution": {"running": "実行中", "pid": "PID: {{pid}}", "exited": "終了しました ({{exitCode}})", "manageCommands": "コマンド権限の管理", "commandManagementDescription": "コマンドの権限を管理します：✓ をクリックして自動実行を許可し、✗ をクリックして実行を拒否します。パターンはオン/オフの切り替えやリストからの削除が可能です。<settingsLink>すべての設定を表示</settingsLink>", "addToAllowed": "許可リストに追加", "removeFromAllowed": "許可リストから削除", "addToDenied": "拒否リストに追加", "removeFromDenied": "拒否リストから削除", "abortCommand": "コマンドの実行を中止", "expandOutput": "出力を展開", "collapseOutput": "出力を折りたたむ", "expandManagement": "コマンド管理セクションを展開", "collapseManagement": "コマンド管理セクションを折りたたむ"}, "response": "応答", "arguments": "引数", "mcp": {"wantsToUseTool": "<PERSON>hanluはMCPサーバー{{serverName}}でツールを使用したい:", "wantsToAccessResource": "<PERSON>hanluはMCPサーバー{{serverName}}のリソースにアクセスしたい:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON>は<code>{{mode}}</code>モードに切り替えたい", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON>は次の理由で<code>{{mode}}</code>モードに切り替えたい: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON><PERSON><code>{{mode}}</code>モードに切り替えました", "didSwitchWithReason": "<PERSON><PERSON><PERSON>は次の理由で<code>{{mode}}</code>モードに切り替えました: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON><PERSON><code>{{mode}}</code>モードで新しいサブタスクを作成したい:", "wantsToFinish": "Zhanluはこのサブタスクを終了したい", "newTaskContent": "サブタスク指示", "completionContent": "サブタスク完了", "resultContent": "サブタスク結果", "defaultResult": "次のタスクに進んでください。", "completionInstructions": "サブタスク完了！結果を確認し、修正や次のステップを提案できます。問題なければ、親タスクに結果を返すために確認してください。"}, "questions": {"hasQuestion": "<PERSON><PERSON>luは質問があります:"}, "taskCompleted": "タスク完了", "powershell": {"issues": "Windows PowerShellに問題があるようです。こちらを参照してください"}, "autoApprove": {"title": "自動承認:", "none": "なし", "description": "自動承認はzhanluに許可を求めずに操作を実行する権限を与えます。完全に信頼できる操作のみ有効にしてください。より詳細な設定は<settingsLink>設定</settingsLink>で利用できます。", "selectOptionsFirst": "自動承認を有効にするには、以下のオプションを少なくとも1つ選択してください", "toggleAriaLabel": "自動承認の切り替え", "disabledAriaLabel": "自動承認が無効です - 最初にオプションを選択してください"}, "reasoning": {"thinking": "考え中", "seconds": "{{count}}秒"}, "contextCondense": {"title": "コンテキスト要約", "condensing": "コンテキストを圧縮中...", "errorHeader": "コンテキストの圧縮に失敗しました", "tokens": "トークン"}, "followUpSuggest": {"copyToInput": "入力欄にコピー（またはShift + クリック）", "autoSelectCountdown": "{{count}}秒後に自動選択します", "countdownDisplay": "{{count}}秒"}, "announcement": {"title": "🎉 湛盧 2.3.2 バージョンアップデート", "description": "バグ修正、コード補完", "whatsNew": "重要なアップデート", "feature1": "<bold>コード基盤モード追加</bold>: コード基盤モードがプログラミング演習問題を生成", "feature2": "<bold>履歴タスクバグ修正</bold>: 新規タスクが履歴タスクに表示される問題を解決", "feature3": "<bold>ちらつき問題修正</bold>: コード表示時の偶発的な画面ちらつき表示問題を解決", "feature4": "<bold>その他の最適化</bold>: その他の問題の最適化", "hideButton": "お知らせを非表示", "detailsDiscussLinks": "<discordLink>詳細ドキュメント</discordLink>でさらに多くの機能を確認 🚀"}, "browser": {"rooWantsToUse": "Zhanluはブラウザを使用したい:", "consoleLogs": "コンソールログ", "noNewLogs": "(新しいログはありません)", "screenshot": "ブラウザのスクリーンショット", "cursor": "カーソル", "navigation": {"step": "ステップ {{current}} / {{total}}", "previous": "前へ", "next": "次へ"}, "sessionStarted": "ブラウザセッション開始", "actions": {"title": "ブラウザアクション: ", "launch": "{{url}} でブラウザを起動", "click": "クリック ({{coordinate}})", "type": "入力 \"{{text}}\"", "scrollDown": "下にスクロール", "scrollUp": "上にスクロール", "close": "ブラウザを閉じる"}}, "codeblock": {"tooltips": {"expand": "コードブロックを展開", "collapse": "コードブロックを折りたたむ", "enable_wrap": "折り返しを有効化", "disable_wrap": "折り返しを無効化", "copy_code": "コードをコピー"}}, "qucikInstructions": {"UiToCode": "UI設計図生成コード", "UmlToCode": "UMLマップ生成コード", "ExplainCode": "コード解釈", "FixCode": "コード誤り訂正", "ImproveCode": "コード最適化", "UnitTest": "ユニットテスト", "CODE_REVIEW": "コードレビュー", "CommentCode": "コードコメント", "PlusButtonClicked": "クリアダイアログ"}, "systemPromptWarning": "警告：カスタムシステムプロンプトの上書きが有効です。これにより機能が深刻に損なわれ、予測不可能な動作が発生する可能性があります。", "profileViolationWarning": "現在のプロファイルは組織の設定と互換性がありません", "shellIntegration": {"title": "コマンド実行警告", "description": "コマンドはVSCodeターミナルシェル統合なしで実行されています。この警告を非表示にするには、<settingsLink>Zhanlu設定</settingsLink>の<strong>Terminal</strong>セクションでシェル統合を無効にするか、以下のリンクを使用してVSCodeターミナル統合のトラブルシューティングを行ってください。", "troubleshooting": "シェル統合のドキュメントはこちらをクリック"}, "ask": {"autoApprovedRequestLimitReached": {"title": "自動承認リクエスト制限に達しました", "description": "Rooは{{count}}件のAPI自動承認リクエスト制限に達しました。カウントをリセットしてタスクを続行しますか？", "button": "リセットして続行"}}, "codebaseSearch": {"wantsToSearch": "Rooはコードベースで <code>{{query}}</code> を検索したい:", "wantsToSearchWithPath": "<PERSON><PERSON>は <code>{{path}}</code> 内のコードベースで <code>{{query}}</code> を検索したい:", "didSearch_one": "1件の結果が見つかりました", "didSearch_other": "{{count}}件の結果が見つかりました", "resultTooltip": "類似度スコア: {{score}} (クリックしてファイルを開く)"}, "read-batch": {"approve": {"title": "すべて承認"}, "deny": {"title": "すべて拒否"}}, "indexingStatus": {"ready": "インデックス準備完了", "indexing": "インデックス作成中 {{percentage}}%", "indexed": "インデックス作成済み", "error": "インデックスエラー", "status": "インデックス状態"}, "versionIndicator": {"ariaLabel": "バージョン {{version}} - クリックしてリリースノートを表示"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> Cloud が間もなく登場！", "description": "クラウドでリモートエージェントを実行し、どこからでもタスクにアクセスし、他の人と協力し、その他多くの機能を利用できます。", "joinWaitlist": "早期アクセスを取得するためにウェイトリストに参加してください。"}, "editMessage": {"placeholder": "メッセージを編集..."}}