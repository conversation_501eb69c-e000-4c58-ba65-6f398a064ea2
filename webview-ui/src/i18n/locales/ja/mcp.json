{"title": "MCPサーバー", "done": "完了", "marketplace": "MCPマーケットプレイス", "description": "<0>Model Context Protocol</0>は、ローカルで実行されているMCPサーバーとの通信を可能にし、Rooの機能を拡張するための追加ツールやリソースを提供します。<1>コミュニティによって作成されたサーバー</1>を使用したり、Rooにワークフロー専用の新しいツールを作成するよう依頼したりできます（例：「最新のnpmドキュメントを取得するツールを追加する」）。", "instructions": "手順", "enableToggle": {"title": "MCPサーバーを有効にする", "description": "有効にすると、<PERSON>han<PERSON>は高度な機能のためにMCPサーバーと対話できるようになります。MCPを使用していない場合は、これを無効にしてZhanluのtoken使用量を減らすことができます。"}, "enableServerCreation": {"title": "MCPサーバー作成を有効にする", "description": "有効にすると、Zhanluは「新しいツールを追加する...」などのコマンドを通じて新しいMCPサーバーの作成を支援できます。MCPサーバーを作成する必要がない場合は、これを無効にしてZhanluのtoken使用量を減らすことができます。", "hint": "ヒント: APIトークンのコストを抑えたいときは、Rooに新しいMCPサーバーを作らせないときにこの設定をOFFにしてね。"}, "editGlobalMCP": "グローバルMCPを編集", "editProjectMCP": "プロジェクトMCPを編集", "whatIsMcp": "MCP Serverとは何ですか", "learnMoreEditingSettings": "MCP設定ファイルの編集方法を詳しく見る", "tool": {"alwaysAllow": "常に許可", "parameters": "パラメータ", "noDescription": "説明なし", "togglePromptInclusion": "プロンプトへの含有を切り替える"}, "tabs": {"tools": "ツール", "resources": "リソース", "errors": "エラー"}, "emptyState": {"noTools": "ツールが見つかりません", "noResources": "リソースが見つかりません", "noErrors": "エラーが見つかりません"}, "networkTimeout": {"label": "ネットワークタイムアウト", "description": "サーバー応答の最大待機時間", "options": {"15seconds": "15秒", "30seconds": "30秒", "1minute": "1分", "5minutes": "5分", "10minutes": "10分", "15minutes": "15分", "30minutes": "30分", "60minutes": "60分"}}, "deleteDialog": {"title": "MCPサーバーを削除", "description": "本当にMCPサーバー「{{serverName}}」を削除する？この操作は元に戻せないよ。", "cancel": "キャンセル", "delete": "削除"}, "serverStatus": {"retrying": "再試行中...", "retryConnection": "再接続"}, "refreshMCP": "MCPサーバーを更新", "execution": {"running": "実行中", "completed": "完了", "error": "エラー"}}