{"errorBoundary": {"title": "問題が発生しました", "reportText": "このエラーを報告して改善にご協力ください", "githubText": "GitHub Issuesページ", "copyInstructions": "以下のエラーメッセージをコピーして報告に含めてください：", "errorStack": "エラースタック：", "componentStack": "コンポーネントスタック："}, "answers": {"yes": "はい", "no": "いいえ", "cancel": "キャンセル", "remove": "削除", "keep": "保持"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "検索..."}, "mermaid": {"loading": "Mermaidダイアグラムを生成中...", "render_error": "ダイアグラムをレンダリングできません", "file_media": "プロセスのプレビュー", "code": "ソースコードの表示", "buttons": {"zoom": "ズーム", "zoomIn": "拡大", "zoomOut": "縮小", "copy": "コピー", "save": "画像を保存", "viewCode": "コードを表示", "viewDiagram": "ダイアグラムを表示", "close": "閉じる"}, "modal": {"codeTitle": "Mermaidコード"}, "tabs": {"diagram": "ダイアグラム", "code": "コード"}, "feedback": {"imageCopied": "画像をクリップボードにコピーしました", "copyError": "画像のコピーエラー"}}, "file": {"errors": {"invalidDataUri": "無効なデータURI形式", "copyingImage": "画像のコピーエラー: {{error}}", "openingImage": "画像を開く際のエラー: {{error}}", "pathNotExists": "パスが存在しません: {{path}}", "couldNotOpen": "ファイルを開けませんでした: {{error}}", "couldNotOpenGeneric": "ファイルを開けませんでした！"}, "success": {"imageDataUriCopied": "画像データURIをクリップボードにコピーしました"}}, "confirmation": {"deleteMessage": "メッセージを削除", "deleteWarning": "このメッセージを削除すると、会話内の後続のメッセージもすべて削除されます。続行しますか？", "editMessage": "メッセージを編集", "editWarning": "このメッセージを編集すると、会話内の後続のメッセージもすべて削除されます。続行しますか？", "proceed": "続行"}}