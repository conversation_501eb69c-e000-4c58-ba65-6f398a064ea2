{"greeting": "zhanluへようこそ！", "introduction": "組み込みおよび拡張可能なモードを備えたzhanluは、計画、アーキテクチャ設計、コーディング、デバッグ、そして今までにない生産性向上を可能にします。", "notice": "開始するには、この拡張機能にはAPIプロバイダーが必要です。", "start": "さあ、始めましょう！", "routers": {"requesty": {"description": "最適化されたLLMルーター", "incentive": "$1の無料クレジット"}, "openrouter": {"description": "LLMsのための統一インターフェース"}}, "chooseProvider": "Rooが機能するには、APIキーが必要です。", "startRouter": "LLMルーターの使用をお勧めします：", "startCustom": "または、あなた自身のAPIキーを使用できます：", "telemetry": {"title": "Z<PERSON>luの改善にご協力ください", "anonymousTelemetry": "バグの修正と拡張機能の改善のため、匿名のエラーと使用データを送信してください。コード、プロンプト、個人情報は一切送信されません。", "changeSettings": "<settingsLink>設定</settingsLink>の下部でいつでも変更できます", "settings": "設定", "allow": "許可", "deny": "拒否"}, "importSettings": "設定をインポート"}