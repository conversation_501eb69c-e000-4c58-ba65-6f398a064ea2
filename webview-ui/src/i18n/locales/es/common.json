{"errorBoundary": {"title": "Algo salió mal", "reportText": "Ayúdanos a mejorar informando de este error en", "githubText": "nuestra página de Issues de GitHub", "copyInstructions": "Copia y pega el siguiente mensaje de error para incluirlo como parte de tu informe:", "errorStack": "<PERSON><PERSON> errores:", "componentStack": "Pila de componentes:"}, "answers": {"yes": "Sí", "no": "No", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Eliminar", "keep": "<PERSON><PERSON><PERSON>"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "Buscar..."}, "mermaid": {"loading": "Generando diagrama mermaid...", "render_error": "No se puede renderizar el diagrama", "file_media": "Vista previa del proceso", "code": "Exhibición de código fuente", "buttons": {"zoom": "Zoom", "zoomIn": "Ampliar", "zoomOut": "Reducir", "copy": "Copiar", "save": "Guardar imagen", "viewCode": "<PERSON>er código", "viewDiagram": "Ver diagrama", "close": "<PERSON><PERSON><PERSON>"}, "modal": {"codeTitle": "<PERSON><PERSON><PERSON> Mermaid"}, "tabs": {"diagram": "Diagrama", "code": "Código"}, "feedback": {"imageCopied": "Imagen copiada al portapapeles", "copyError": "Error copiando la <PERSON>n"}}, "file": {"errors": {"invalidDataUri": "Formato de URI de datos inválido", "copyingImage": "Error copiando la imagen: {{error}}", "openingImage": "Error abriendo la imagen: {{error}}", "pathNotExists": "La ruta no existe: {{path}}", "couldNotOpen": "No se pudo abrir el archivo: {{error}}", "couldNotOpenGeneric": "¡No se pudo abrir el archivo!"}, "success": {"imageDataUriCopied": "URI de datos de imagen copiada al portapapeles"}}, "confirmation": {"deleteMessage": "Eliminar men<PERSON>", "deleteWarning": "Eliminar este mensaje eliminará todos los mensajes posteriores en la conversación. ¿Deseas continuar?", "editMessage": "<PERSON><PERSON>", "editWarning": "Editar este mensaje eliminará todos los mensajes posteriores en la conversación. ¿Deseas continuar?", "proceed": "<PERSON><PERSON><PERSON><PERSON>"}}