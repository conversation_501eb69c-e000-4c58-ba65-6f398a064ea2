{"recentTasks": "<PERSON><PERSON><PERSON>", "viewAll": "<PERSON>er to<PERSON>", "tokens": "Tokens: ↑{{in}} ↓{{out}}", "cache": "Caché: +{{writes}} → {{reads}}", "apiCost": "Costo de API: ${{cost}}", "history": "Historial", "exitSelectionMode": "Salir del modo selección", "enterSelectionMode": "Entrar en modo selección", "done": "Listo", "searchPlaceholder": "Buscar en el historial...", "newest": "Más recientes", "oldest": "Más antiguas", "mostExpensive": "<PERSON><PERSON>", "mostTokens": "Más tokens", "mostRelevant": "<PERSON><PERSON>", "deleteTaskTitle": "Eliminar tarea (Shift + Clic para omitir confirmación)", "tokensLabel": "Tokens:", "cacheLabel": "Caché:", "apiCostLabel": "Costo de API:", "copyPrompt": "<PERSON><PERSON><PERSON> prompt", "exportTask": "Exportar tarea", "deleteTask": "Eliminar tarea", "deleteTaskMessage": "¿Estás seguro de que quieres eliminar esta tarea? Esta acción no se puede deshacer.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "exitSelection": "Salir de la selección", "selectionMode": "<PERSON><PERSON>", "deselectAll": "<PERSON>elecci<PERSON><PERSON> todo", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "selectedItems": "{{selected}}/{{total}} elementos seleccionados", "clearSelection": "Limpiar <PERSON>", "deleteSelected": "Eliminar seleccionados", "deleteTasks": "Eliminar tareas", "confirmDeleteTasks": "¿Estás seguro de que quieres eliminar {{count}} tareas?", "deleteTasksWarning": "Las tareas eliminadas no se pueden recuperar. Por favor, aseg<PERSON>rate de que quieres continuar.", "deleteItems": "Eliminar {{count}} elementos", "workspace": {"prefix": "Espacio de trabajo:", "current": "Actual", "all": "Todos"}, "sort": {"prefix": "Ordenar:", "newest": "Más recientes", "oldest": "Más antiguas", "mostExpensive": "<PERSON><PERSON>", "mostTokens": "Más tokens", "mostRelevant": "<PERSON><PERSON>"}, "viewAllHistory": "Ver todo el historial"}