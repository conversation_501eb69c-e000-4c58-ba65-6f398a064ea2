{"title": "Servidores MCP", "done": "Listo", "marketplace": "Mercado MCP", "description": "El <0>Model Context Protocol</0> permite la comunicación con servidores MCP que se ejecutan localmente y proporcionan herramientas y recursos adicionales para extender las capacidades de Roo. Puedes usar <1>servidores creados por la comunidad</1> o pedir a zhanlu que cree nuevas herramientas específicas para tu flujo de trabajo (por ejemplo, \"añadir una herramienta que obtenga la documentación más reciente de npm\").", "instructions": "Instrucciones", "enableToggle": {"title": "Habilitar servidores MCP", "description": "<PERSON><PERSON>do está habilitado, <PERSON><PERSON><PERSON> podrá interactuar con servidores MCP para obtener funcionalidades avanzadas. Si no usas MCP, puedes desactivarlo para reducir el uso de tokens de Zhanlu."}, "enableServerCreation": {"title": "Habilitar creación de servidores MCP", "description": "<PERSON><PERSON>do está habilitado, <PERSON><PERSON><PERSON> puede ayudarte a crear nuevos servidores MCP mediante comandos como \"añadir una nueva herramienta para...\". Si no necesitas crear servidores MCP, puedes desactivar esto para reducir el uso de tokens de Zhanlu.", "hint": "Consejo: Para reducir los costes de tokens API, desactiva esta opción cuando no le pidas a zhanlu que cree un nuevo servidor MCP."}, "editGlobalMCP": "Editar MCP Global", "editProjectMCP": "Editar MCP del Proyecto", "whatIsMcp": "¿Qué es el servidor MCP", "learnMoreEditingSettings": "Más información sobre cómo editar archivos de configuración MCP", "tool": {"alwaysAllow": "<PERSON><PERSON><PERSON>", "parameters": "Parámetros", "noDescription": "Sin descripción", "togglePromptInclusion": "Alternar inclusión en el prompt"}, "tabs": {"tools": "Herramientas", "resources": "Recursos", "errors": "Errores"}, "emptyState": {"noTools": "No se encontraron herramientas", "noResources": "No se encontraron recursos", "noErrors": "No se encontraron errores"}, "networkTimeout": {"label": "Tiempo de espera de red", "description": "Tiempo máximo de espera para respuestas del servidor", "options": {"15seconds": "15 segundos", "30seconds": "30 segundos", "1minute": "1 minuto", "5minutes": "5 minutos", "10minutes": "10 minutos", "15minutes": "15 minutos", "30minutes": "30 minutos", "60minutes": "60 minutos"}}, "deleteDialog": {"title": "Eliminar servidor <PERSON>", "description": "¿Seguro que quieres eliminar el servidor MCP \"{{serverName}}\"? Esta acción no se puede deshacer.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar"}, "serverStatus": {"retrying": "Reintentando...", "retryConnection": "Reintentar conexión"}, "refreshMCP": "Act<PERSON>iza<PERSON>", "execution": {"running": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "completed": "Completado", "error": "Error"}}