{"greeting": "Bienvenido a Zhanlu", "task": {"title": "Tarea", "seeMore": "<PERSON>er más", "seeLess": "<PERSON>er menos", "tokens": "Tokens:", "cache": "Caché:", "apiCost": "Costo de API:", "contextWindow": "Longitud del contexto:", "closeAndStart": "Cerrar tarea e iniciar una nueva", "export": "Exportar historial de tareas", "delete": "Eliminar tarea (Shift + Clic para omitir confirmación)", "condenseContext": "Condensar contexto de forma inteligente", "share": "Compartir tarea", "copyId": "Copiar ID de tarea", "shareWithOrganization": "Compartir con organización", "shareWithOrganizationDescription": "Solo los miembros de tu organización pueden acceder", "sharePublicly": "Compartir públicamente", "sharePubliclyDescription": "Cualquiera con el enlace puede acceder", "connectToCloud": "Conectar al Cloud", "connectToCloudDescription": "Inicia sesión en zhanlu Cloud para compartir tareas", "sharingDisabledByOrganization": "Compartir deshabilitado por la organización", "shareSuccessOrganization": "Enlace de organización copiado al portapapeles", "shareSuccessPublic": "Enlace público copiado al portapapeles"}, "unpin": "<PERSON><PERSON><PERSON>", "pin": "<PERSON><PERSON>", "retry": {"title": "Reintentar", "tooltip": "Intenta la operación de nuevo"}, "startNewTask": {"title": "Iniciar nueva tarea", "tooltip": "Comienza una nueva tarea"}, "proceedAnyways": {"title": "Con<PERSON><PERSON><PERSON> de todos modos", "tooltip": "Continuar mientras se ejecuta el comando"}, "save": {"title": "Guardar", "tooltip": "Guardar los cambios del mensaje"}, "tokenProgress": {"availableSpace": "Espacio disponible: {{amount}} tokens", "tokensUsed": "Tokens utilizados: {{used}} de {{total}}", "reservedForResponse": "Reservado para respuesta del modelo: {{amount}} tokens"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Re<PERSON>zar esta acción"}, "completeSubtaskAndReturn": "Completar subtarea y regresar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprobar esta acción"}, "runCommand": {"title": "Ejecutar comando", "tooltip": "Ejecutar este comando"}, "proceedWhileRunning": {"title": "Continuar mientras se ejecuta", "tooltip": "Continuar a pesar de las advertencias"}, "killCommand": {"title": "Terminar comando", "tooltip": "Terminar el comando actual"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> la tarea actual"}, "terminate": {"title": "Terminar", "tooltip": "Terminar la tarea actual"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Cancelar la operación actual"}, "scrollToBottom": "Desplazarse al final del chat", "about": "<PERSON><PERSON>, corri<PERSON>, refactoriza y depura código con la ayuda del Modelo de IA Zhanlu.<br />Consulta nuestra <DocsLink>documentación</DocsLink> para más información.", "onboarding": "La lista de tareas en este espacio de trabajo está vacía. Comienza escribiendo tu tarea abajo.<br>¿No estás seguro de cómo empezar? Lee más en nuestra <DocsLink>documentación</DocsLink>.", "zhanluTips": {"architectMode": {"title": "<PERSON><PERSON>", "description": "Crea planes detallados para implementar soluciones"}, "codeMode": {"title": "<PERSON><PERSON>", "description": "Escribe y modifica código siguiendo mejores prácticas"}, "testMode": {"title": "Modo Prueba Unitaria", "description": "Genera pruebas completas para tu código"}, "projectFixMode": {"title": "Modo Corrección de Proyectos", "description": "Identifica y repara defectos en proyectos"}, "sastMode": {"title": "Modo Correcci<PERSON> de Seguridad", "description": "Corrige vulnerabilidades de seguridad en el código"}, "codeReviewMode": {"title": "Modo Revisión de Código", "description": "Evalúa la calidad del código y sugiere mejoras"}, "readmeMode": {"title": "Modo Documentación", "description": "Crea documentación técnica detallada"}, "simpleMode": {"title": "Modo Preguntas y Respuestas", "description": "Responde preguntas técnicas con precisión"}, "boomerangTasks": {"title": "Orquestación de Tareas", "description": "Divide las tareas en partes más pequeñas y manejables."}, "stickyModels": {"title": "Modos persistentes", "description": "Cada modo recuerda tu último modelo utilizado"}, "tools": {"title": "Herramientas", "description": "Permite que la IA resuelva problemas navegando por la web, ejecutando comandos y mucho más."}, "customizableModes": {"title": "Modos personalizables", "description": "Personalidades especializadas con sus propios comportamientos y modelos asignados"}}, "selectMode": "Seleccionar modo de interacción", "selectApiConfig": "Seleccionar configuración de API", "internetSearch": "Después de abrir la recuperación de internet, puede buscar contenido relevante en Internet.", "internetSearchClosed": "Cierre la búsqueda por Internet", "enhancePrompt": "Mejorar el mensaje con contexto adicional", "addImages": "Agregar imágenes al mensaje", "sendMessage": "<PERSON><PERSON><PERSON> men<PERSON>", "stopTts": "Detener texto a voz", "typeMessage": "Escribe un mensaje...", "typeTask": "Escribe tu tarea aquí...", "addContext": "@ aña<PERSON> contexto, / cambiar modo, # instrucciones rápidas", "dragFiles": "Mantén Shift y arrastra archivos", "dragFilesImages": "Mantén Shift y arrastra archivos/imágenes", "enhancePromptDescription": "El botón 'Mejorar el mensaje' ayuda a mejorar tu petición proporcionando contexto adicional, aclaraciones o reformulaciones. Intenta escribir una petición aquí y haz clic en el botón nuevamente para ver cómo funciona.", "modeSelector": {"title": "Modos", "marketplace": "Marketplace de Modos", "settings": "Configuración de Modos", "description": "Personalidades especializadas que adaptan el comportamiento de Roo."}, "errorReadingFile": "Error al leer el archivo:", "noValidImages": "No se procesaron imágenes válidas", "separator": "Separador", "edit": "Editar...", "forNextMode": "para el siguiente modo", "forPreviousMode": "para el modo anterior", "error": "Error", "warning": "Advertencia", "diffError": {"title": "Edición fallida"}, "troubleMessage": "Z<PERSON>lu está teniendo problemas...", "apiRequest": {"title": "Solicitud API", "failed": "Solicitud API falló", "streaming": "Solicitud API...", "cancelled": "Solicitud API cancelada", "streamingFailed": "Transmisión API falló"}, "checkpoint": {"initial": "Punto de control inicial", "regular": "Punto de control", "initializingWarning": "Todavía inicializando el punto de control... Si esto tarda demasiado, puedes desactivar los puntos de control en la <settingsLink>configuración</settingsLink> y reiniciar tu tarea.", "menu": {"viewDiff": "Ver diferencias", "restore": "Restaurar punto de control", "restoreFiles": "Restaurar archivos", "restoreFilesDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto.", "restoreFilesAndTask": "Restaurar archivos y tarea", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Esta acción no se puede deshacer.", "restoreFilesAndTaskDescription": "Restaura los archivos de tu proyecto a una instantánea tomada en este punto y elimina todos los mensajes posteriores a este punto."}, "current": "Actual"}, "instructions": {"wantsToFetch": "<PERSON><PERSON><PERSON> quiere obtener instrucciones detalladas para ayudar con la tarea actual"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON><PERSON> quiere leer este archivo:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON><PERSON> quiere leer este archivo fuera del espacio de trabajo:", "didRead": "<PERSON><PERSON><PERSON> leyó este archivo:", "wantsToEdit": "<PERSON><PERSON><PERSON> quiere editar este archivo:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON><PERSON> quiere editar este archivo fuera del espacio de trabajo:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> quiere editar un archivo de configuración protegido:", "wantsToCreate": "<PERSON><PERSON><PERSON> quiere crear un nuevo archivo:", "wantsToSearchReplace": "<PERSON><PERSON><PERSON> quiere realizar búsqueda y reemplazo en este archivo:", "didSearchReplace": "Zhanlu realizó búsqueda y reemplazo en este archivo:", "wantsToInsert": "<PERSON><PERSON><PERSON> quiere insertar contenido en este archivo:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> quiere insertar contenido en este archivo en la línea {{lineNumber}}:", "wantsToInsertAtEnd": "<PERSON><PERSON><PERSON> quiere añadir contenido al final de este archivo:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> quiere leer este archivo y {{count}} más:", "wantsToReadMultiple": "<PERSON><PERSON><PERSON> quiere leer varios archivos:", "wantsToApplyBatchChanges": "<PERSON><PERSON><PERSON> quiere aplicar cambios a múltiples archivos:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON><PERSON> quiere ver los archivos de nivel superior en este directorio:", "didViewTopLevel": "Zhanlu vio los archivos de nivel superior en este directorio:", "wantsToViewRecursive": "<PERSON><PERSON><PERSON> quiere ver recursivamente todos los archivos en este directorio:", "didViewRecursive": "Zhanlu vio recursivamente todos los archivos en este directorio:", "wantsToViewDefinitions": "<PERSON><PERSON><PERSON> quiere ver nombres de definiciones de código fuente utilizados en este directorio:", "didViewDefinitions": "Zhanlu vio nombres de definiciones de código fuente utilizados en este directorio:", "wantsToSearch": "<PERSON><PERSON><PERSON> quiere buscar en este directorio <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON><PERSON> bus<PERSON> en este directorio <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> quiere buscar en este directorio (fuera del espacio de trabajo) <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> bus<PERSON> en este directorio (fuera del espacio de trabajo) <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> quiere ver los archivos de nivel superior en este directorio (fuera del espacio de trabajo):", "didViewTopLevelOutsideWorkspace": "Zhanlu vio los archivos de nivel superior en este directorio (fuera del espacio de trabajo):", "wantsToViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> quiere ver recursivamente todos los archivos en este directorio (fuera del espacio de trabajo):", "didViewRecursiveOutsideWorkspace": "Zhanlu vio recursivamente todos los archivos en este directorio (fuera del espacio de trabajo):", "wantsToViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON> quiere ver nombres de definiciones de código fuente utilizados en este directorio (fuera del espacio de trabajo):", "didViewDefinitionsOutsideWorkspace": "Zhanlu vio nombres de definiciones de código fuente utilizados en este directorio (fuera del espacio de trabajo):"}, "commandOutput": "Salida del comando", "commandExecution": {"running": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pid": "PID: {{pid}}", "exited": "Finalizado ({{exitCode}})", "manageCommands": "Gestionar permisos de comandos", "commandManagementDescription": "Gestionar permisos de comandos: Haz clic en ✓ para permitir la ejecución automática, ✗ para denegar la ejecución. Los patrones se pueden activar/desactivar o eliminar de las listas. <settingsLink>Ver todos los ajustes</settingsLink>", "addToAllowed": "Añadir a la lista de permitidos", "removeFromAllowed": "Eliminar de la lista de permitidos", "addToDenied": "Añadir a la lista de denegados", "removeFromDenied": "Eliminar de la lista de denegados", "abortCommand": "Abortar ejecución del comando", "expandOutput": "<PERSON><PERSON><PERSON> salida", "collapseOutput": "<PERSON><PERSON><PERSON> salida", "expandManagement": "Expandir sección de gestión de comandos", "collapseManagement": "Contraer sección de gestión de comandos"}, "response": "Respuesta", "arguments": "Argumentos", "mcp": {"wantsToUseTool": "<PERSON><PERSON><PERSON> quiere usar una herramienta en el servidor MCP {{serverName}}:", "wantsToAccessResource": "<PERSON><PERSON><PERSON> quiere acceder a un recurso en el servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> quiere cambiar a modo <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> quiere cambiar a modo <code>{{mode}}</code> porque: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> cambió a modo <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON><PERSON> cambió a modo <code>{{mode}}</code> porque: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> quiere crear una nueva subtarea en modo <code>{{mode}}</code>:", "wantsToFinish": "<PERSON><PERSON><PERSON> quiere finalizar esta subtarea", "newTaskContent": "Instrucciones de la subtarea", "completionContent": "Subtarea completada", "resultContent": "Resultados de la subtarea", "defaultResult": "Por favor, continúa con la siguiente tarea.", "completionInstructions": "¡Subtarea completada! Puedes revisar los resultados y sugerir correcciones o próximos pasos. Si todo se ve bien, confirma para devolver el resultado a la tarea principal."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> tiene una pregunta:"}, "taskCompleted": "<PERSON><PERSON> completada", "powershell": {"issues": "Parece que estás teniendo problemas con Windows PowerShell, por favor consulta esta"}, "autoApprove": {"title": "Auto-aprobar:", "none": "<PERSON><PERSON><PERSON>", "description": "Auto-aprobar permite a zhanlu realizar acciones sin pedir permiso. Habilita solo para acciones en las que confíes plenamente. Configuración más detallada disponible en <settingsLink>Configuración</settingsLink>.", "selectOptionsFirst": "Selecciona al menos una opción a continuación para habilitar la aprobación automática", "toggleAriaLabel": "Alternar aprobación automática", "disabledAriaLabel": "Aprobación automática desactivada: seleccione primero las opciones"}, "reasoning": {"thinking": "Pensando", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contexto condensado", "condensing": "Condensando contexto...", "errorHeader": "Error al condensar el contexto", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "Copiar a la entrada (o Shift + clic)", "autoSelectCountdown": "Selección automática en {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Actualización de la versión 2.3.2 de <PERSON>lu", "description": "Corrección de errores, autocompletado de código", "whatsNew": "Actualizaciones importantes", "feature1": "<bold>Modo Fundación de Código añadido</bold>: El modo Fundación de Código genera ejercicios de programación", "feature2": "<bold>Corrección de bug de tareas de historial</bold>: <PERSON><PERSON> resuelto donde las nuevas tareas aparecen en las tareas de historial", "feature3": "<bold>Corrección del problema de parpadeo</bold>: Problema de parpadeo ocasional de pantalla al mostrar código resuelto", "feature4": "<bold>Otras optimizaciones</bold>: Optimizaciones de varios otros problemas", "hideButton": "<PERSON><PERSON><PERSON><PERSON> anuncio", "detailsDiscussLinks": "Consulta la <discordLink>documentación detallada</discordLink> para conocer más funciones 🚀"}, "browser": {"rooWantsToUse": "<PERSON><PERSON><PERSON> quiere usar el navegador:", "consoleLogs": "Registros de la consola", "noNewLogs": "(No hay nuevos registros)", "screenshot": "Captura de pantalla del navegador", "cursor": "cursor", "navigation": {"step": "Paso {{current}} de {{total}}", "previous": "Anterior", "next": "Siguient<PERSON>"}, "sessionStarted": "Sesión de navegador iniciada", "actions": {"title": "Acción de navegación: ", "launch": "Iniciar <PERSON> en {{url}}", "click": "Clic ({{coordinate}})", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON><PERSON> hacia abajo", "scrollUp": "<PERSON><PERSON><PERSON><PERSON> hacia arriba", "close": "<PERSON><PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Expandir bloque de código", "collapse": "Contraer bloque de código", "enable_wrap": "Activar ajuste de línea", "disable_wrap": "Desactivar ajuste de línea", "copy_code": "<PERSON><PERSON>r c<PERSON>"}}, "qucikInstructions": {"UiToCode": "Código de generación de dibujos de diseño de la interfaz de usuario", "UmlToCode": "Código de generación de gráficos Umu", "ExplainCode": "Interpretación del Código", "FixCode": "Corrección de errores de código", "ImproveCode": "Optimización de código", "UnitTest": "Prueba unitaria", "CODE_REVIEW": "Revisión del Código", "CommentCode": "Comentario de código", "PlusButtonClicked": "Diálogo de vaciado"}, "systemPromptWarning": "ADVERTENCIA: Anulación de instrucciones del sistema personalizada activa. Esto puede romper gravemente la funcionalidad y causar un comportamiento impredecible.", "profileViolationWarning": "El perfil actual no es compatible con la configuración de tu organización", "shellIntegration": {"title": "Advertencia de ejecución de comandos", "description": "Tu comando se está ejecutando sin la integración de shell de terminal de VSCode. Para suprimir esta advertencia, puedes desactivar la integración de shell en la sección <strong>Terminal</strong> de la <settingsLink>configuración de Zhanlu</settingsLink> o solucionar problemas de integración de terminal de VSCode usando el enlace de abajo.", "troubleshooting": "Haz clic aquí para ver la documentación de integración de shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Límite de Solicitudes Auto-aprobadas Alcanzado", "description": "z<PERSON><PERSON> ha alcanzado el límite auto-aprobado de {{count}} solicitud(es) API. ¿Deseas reiniciar el contador y continuar con la tarea?", "button": "Reiniciar y Continuar"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON><PERSON> quiere buscar en la base de código <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> quiere buscar en la base de código <code>{{query}}</code> en <code>{{path}}</code>:", "didSearch_one": "Se encontró 1 resultado", "didSearch_other": "Se encontraron {{count}} resultados", "resultTooltip": "Puntuación de similitud: {{score}} (haz clic para abrir el archivo)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON> todo"}, "deny": {"title": "<PERSON><PERSON><PERSON> todo"}}, "indexingStatus": {"ready": "Índice listo", "indexing": "Indexando {{percentage}}%", "indexed": "Indexado", "error": "<PERSON><PERSON><PERSON>", "status": "Estado del índice"}, "versionIndicator": {"ariaLabel": "Versión {{version}} - Haz clic para ver las notas de la versión"}, "zhanluCloudCTA": {"title": "¡zhanlu Cloud llegará pronto!", "description": "Ejecuta agentes remotos en la nube, accede a tus tareas desde cualquier lugar, colabora con otros y mucho más.", "joinWaitlist": "Únete a la lista de espera para obtener acceso anticipado."}, "editMessage": {"placeholder": "Edita tu mensaje..."}}