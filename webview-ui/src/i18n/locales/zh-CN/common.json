{"errorBoundary": {"title": "出现了错误", "reportText": "请通过在以下位置报告此错误来帮助我们改进", "githubText": "我们的 GitHub Issues 页面", "copyInstructions": "复制并粘贴以下错误信息，将其作为提交内容的一部分：", "errorStack": "错误堆栈：", "componentStack": "组件堆栈："}, "answers": {"yes": "是", "no": "否", "cancel": "取消", "remove": "移除", "keep": "保留"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "搜索..."}, "mermaid": {"loading": "生成 Mermaid 图表中...", "render_error": "无法渲染图表", "file_media": "流程预览", "code": "源码展示", "buttons": {"zoom": "缩放", "zoomIn": "放大", "zoomOut": "缩小", "copy": "复制", "save": "保存图片", "viewCode": "查看代码", "viewDiagram": "查看图表", "close": "关闭"}, "modal": {"codeTitle": "Mermaid 代码"}, "tabs": {"diagram": "图表", "code": "代码"}, "feedback": {"imageCopied": "图片已复制到剪贴板", "copyError": "复制图片时出错"}}, "file": {"errors": {"invalidDataUri": "无效的数据 URI 格式", "copyingImage": "复制图片时出错: {{error}}", "openingImage": "打开图片时出错: {{error}}", "pathNotExists": "路径不存在: {{path}}", "couldNotOpen": "无法打开文件: {{error}}", "couldNotOpenGeneric": "无法打开文件!"}, "success": {"imageDataUriCopied": "图片数据 URI 已复制到剪贴板"}}, "confirmation": {"deleteMessage": "删除消息", "deleteWarning": "删除此消息将删除对话中的所有后续消息。是否继续？", "editMessage": "编辑消息", "editWarning": "编辑此消息将删除对话中的所有后续消息。是否继续？", "proceed": "继续"}}