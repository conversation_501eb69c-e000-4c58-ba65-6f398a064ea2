{"greeting": "Добро пожаловать в Zhanlu", "task": {"title": "Задача", "seeMore": "Показать больше", "seeLess": "Показать меньше", "tokens": "Токенов:", "cache": "Кэш:", "apiCost": "Стоимость API:", "contextWindow": "<PERSON><PERSON><PERSON><PERSON> контекста:", "closeAndStart": "Закрыть задачу и начать новую", "export": "Экспортировать историю задач", "delete": "Удалить задачу (Shift + клик для пропуска подтверждения)", "condenseContext": "Интеллектуально сжать контекст", "share": "Поделиться задачей", "shareWithOrganization": "Поделиться с организацией", "shareWithOrganizationDescription": "Только члены вашей организации могут получить доступ", "sharePublicly": "Поделиться публично", "sharePubliclyDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, у кого есть ссылка, может получить доступ", "connectToCloud": "Подключиться к облаку", "connectToCloudDescription": "Войди в z<PERSON><PERSON>, чтобы делиться задачами", "sharingDisabledByOrganization": "Об<PERSON>ен отключен организацией", "shareSuccessOrganization": "Ссылка организации скопирована в буфер обмена", "shareSuccessPublic": "Публичная ссылка скопирована в буфер обмена"}, "unpin": "Открепить", "pin": "Закрепить", "retry": {"title": "Повторить", "tooltip": "Попробовать выполнить операцию снова"}, "startNewTask": {"title": "Начать новую задачу", "tooltip": "Начать новую задачу"}, "proceedAnyways": {"title": "Все равно продолжить", "tooltip": "Продолжить выполнение команды"}, "save": {"title": "Сохранить", "tooltip": "Сохранить изменения сообщения"}, "tokenProgress": {"availableSpace": "Доступно места: {{amount}} токенов", "tokensUsed": "Использовано токенов: {{used}} из {{total}}", "reservedForResponse": "Зарезервировано для ответа модели: {{amount}} токенов"}, "reject": {"title": "Отклонить", "tooltip": "Отклонить это действие"}, "completeSubtaskAndReturn": "Завершить подзадачу и вернуться", "approve": {"title": "Одобрить", "tooltip": "Одобрить это действие"}, "runCommand": {"title": "Выполнить команду", "tooltip": "Выполнить эту команду"}, "proceedWhileRunning": {"title": "Продолжить во время выполнения", "tooltip": "Продолжить несмотря на предупреждения"}, "resumeTask": {"title": "Возобновить задачу", "tooltip": "Продолжить текущую задачу"}, "killCommand": {"title": "Завер<PERSON>ить команду", "tooltip": "Завершить текущую команду"}, "terminate": {"title": "Завершить", "tooltip": "Завершить текущую задачу"}, "cancel": {"title": "Отмена", "tooltip": "Отменить текущую операцию"}, "scrollToBottom": "Прокрутить к концу чата", "about": "Генерируйте, исправляйте, рефакторите и отлаживайте код с помощью ИИ-модели Zhanlu.<br />Изучите нашу <DocsLink>документацию</DocsLink> для получения дополнительной информации.", "zhanluTips": {"architectMode": {"title": "Режим Архитектора", "description": "Создает детальные планы для реализации решений"}, "codeMode": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Кода", "description": "Пишет и модифицирует код согласно лучшим практикам"}, "testMode": {"title": "Режим Тестирования", "description": "Генерирует комплексные тесты для вашего кода"}, "projectFixMode": {"title": "Режим Исправления Проекта", "description": "Выявляет и устраняет дефекты в проектах"}, "sastMode": {"title": "Режим Устранения Уязвимостей", "description": "Исправляет уязвимости безопасности в коде"}, "codeReviewMode": {"title": "Режим Ревью Кода", "description": "Оценивает качество кода и предлагает улучшения"}, "readmeMode": {"title": "Режим Документации", "description": "Создает подробную техническую документацию"}, "simpleMode": {"title": "Режим Вопрос-Ответ", "description": "Точно отвечает на технические вопросы"}, "boomerangTasks": {"title": "Оркестрация задач", "description": "Разделяйте задачи на более мелкие, управляемые части"}, "stickyModels": {"title": "Липкие режимы", "description": "Каждый режим запоминает вашу последнюю использованную модель"}, "tools": {"title": "Инструменты", "description": "Разрешите ИИ решать проблемы, просматривая веб-страницы, выполняя команды и т. д."}, "customizableModes": {"title": "Настраиваемые режимы", "description": "Специализированные персонажи с собственным поведением и назначенными моделями"}}, "onboarding": "Список задач в этой рабочей области пуст. Начните, введя свою задачу ниже.<br>Не знаете, как начать? Прочтите нашу <DocsLink>документацию</DocsLink>.", "selectMode": "Выбрать режим взаимодействия", "selectApiConfig": "Выбрать конфигурацию API", "internetSearch": "После открытия поиска в Интернете вы можете найти соответствующий контент в Интернете", "internetSearchClosed": "Закрыть поиск в Интернете", "enhancePrompt": "Улучшить запрос с дополнительным контекстом", "enhancePromptDescription": "Кнопка 'Улучшить запрос' помогает сделать ваш запрос лучше, предоставляя дополнительный контекст, уточнения или переформулировку. Попробуйте ввести запрос и снова нажать кнопку, чтобы увидеть, как это работает.", "modeSelector": {"title": "Режимы", "marketplace": "Маркетплейс режимов", "settings": "Настройки режимов", "description": "Специализированные персоны, которые настраивают поведение Roo."}, "addImages": "Добавить изображения к сообщению", "sendMessage": "Отправить сообщение", "stopTts": "Остановить синтез речи", "typeMessage": "Введите сообщение...", "typeTask": "Введите вашу задачу здесь...", "addContext": "@ Добавить контекст, / Переключить режим, # Быстрые команды", "dragFiles": "Удерживайте Shift и перетаскивайте файлы", "dragFilesImages": "Удерживайте Shift и перетаскивайте файлы/изображения", "errorReadingFile": "Ошибка чтения файла:", "noValidImages": "Не удалось обработать ни одно изображение", "separator": "Разделитель", "edit": "Редактировать...", "forNextMode": "для следующего режима", "forPreviousMode": "для предыдущего режима", "apiRequest": {"title": "API-запрос", "failed": "API-запрос не выполнен", "streaming": "API-запрос...", "cancelled": "API-запрос отменен", "streamingFailed": "Ошибка потокового API-запроса"}, "checkpoint": {"initial": "Начальная точка сохранения", "regular": "Точка сохранения", "initializingWarning": "Точка сохранения еще инициализируется... Если это занимает слишком много времени, вы можете отключить точки сохранения в <settingsLink>настройках</settingsLink> и перезапустить задачу.", "menu": {"viewDiff": "Просмотреть различия", "restore": "Восстановить точку сохранения", "restoreFiles": "Восстановить файлы", "restoreFilesDescription": "Восстанавливает файлы вашего проекта до состояния на момент этой точки.", "restoreFilesAndTask": "Восстановить файлы и задачу", "confirm": "Подтвердить", "cancel": "Отмена", "cannotUndo": "Это действие нельзя отменить.", "restoreFilesAndTaskDescription": "Восстанавливает файлы проекта до состояния на момент этой точки и удаляет все сообщения после нее."}, "current": "Текущая"}, "instructions": {"wantsToFetch": "Zhanlu хочет получить подробные инструкции для помощи с текущей задачей"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON><PERSON> хочет прочитать этот файл:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON><PERSON> хочет прочитать этот файл вне рабочей области:", "didRead": "Zhanlu прочитал этот файл:", "wantsToEdit": "<PERSON><PERSON><PERSON> хочет отредактировать этот файл:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON><PERSON> хочет отредактировать этот файл вне рабочей области:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> хочет отредактировать защищённый файл конфигурации:", "wantsToCreate": "<PERSON><PERSON><PERSON> хочет создать новый файл:", "wantsToSearchReplace": "Z<PERSON>lu хочет выполнить поиск и замену в этом файле:", "didSearchReplace": "Zhanlu выполнил поиск и замену в этом файле:", "wantsToInsert": "<PERSON><PERSON><PERSON> хочет вставить содержимое в этот файл:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> хочет вставить содержимое в этот файл на строку {{lineNumber}}:", "wantsToInsertAtEnd": "Z<PERSON><PERSON> хочет добавить содержимое в конец этого файла:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> хочет прочитать этот файл и еще {{count}}:", "wantsToReadMultiple": "<PERSON><PERSON><PERSON> хочет прочитать несколько файлов:", "wantsToApplyBatchChanges": "<PERSON><PERSON><PERSON> хочет применить изменения к нескольким файлам:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON><PERSON> хочет просмотреть файлы верхнего уровня в этой директории:", "didViewTopLevel": "Zhanlu просмотрел файлы верхнего уровня в этой директории:", "wantsToViewRecursive": "Zhanlu хочет рекурсивно просмотреть все файлы в этой директории:", "didViewRecursive": "Zhanlu рекурсивно просмотрел все файлы в этой директории:", "wantsToViewDefinitions": "<PERSON><PERSON><PERSON> хочет просмотреть имена определений исходного кода в этой директории:", "didViewDefinitions": "Zhanlu просмотрел имена определений исходного кода в этой директории:", "wantsToSearch": "<PERSON><PERSON><PERSON> хочет выполнить поиск в этой директории по <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON>lu выполнил поиск в этой директории по <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "Z<PERSON>lu хочет выполнить поиск в этой директории (вне рабочего пространства) по <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "Zhanlu выполнил поиск в этой директории (вне рабочего пространства) по <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Zhanlu хочет просмотреть файлы верхнего уровня в этой директории (вне рабочего пространства):", "didViewTopLevelOutsideWorkspace": "Zhanlu просмотрел файлы верхнего уровня в этой директории (вне рабочего пространства):", "wantsToViewRecursiveOutsideWorkspace": "Zhanlu хочет рекурсивно просмотреть все файлы в этой директории (вне рабочего пространства):", "didViewRecursiveOutsideWorkspace": "Zhanlu рекурсивно просмотрел все файлы в этой директории (вне рабочего пространства):", "wantsToViewDefinitionsOutsideWorkspace": "Zhanlu хочет просмотреть имена определений исходного кода в этой директории (вне рабочего пространства):", "didViewDefinitionsOutsideWorkspace": "Zhanlu просмотрел имена определений исходного кода в этой директории (вне рабочего пространства):"}, "commandOutput": "Вывод команды", "commandExecution": {"running": "Выполняется", "pid": "PID: {{pid}}", "exited": "Завершено ({{exitCode}})", "manageCommands": "Управление разрешениями команд", "commandManagementDescription": "Управляйте разрешениями команд: Нажмите ✓, чтобы разрешить автоматическое выполнение, ✗, чтобы запретить выполнение. Шаблоны можно включать/выключать или удалять из списков. <settingsLink>Просмотреть все настройки</settingsLink>", "addToAllowed": "Добавить в список разрешенных", "removeFromAllowed": "Удалить из списка разрешенных", "addToDenied": "Добавить в список запрещенных", "removeFromDenied": "Удалить из списка запрещенных", "abortCommand": "Прервать выполнение команды", "expandOutput": "Развернуть вывод", "collapseOutput": "Свернуть вывод", "expandManagement": "Развернуть раздел управления командами", "collapseManagement": "Свернуть раздел управления командами"}, "response": "Ответ", "arguments": "Аргументы", "mcp": {"wantsToUseTool": "<PERSON><PERSON><PERSON> хочет использовать инструмент на сервере MCP {{serverName}}:", "wantsToAccessResource": "<PERSON><PERSON><PERSON> хочет получить доступ к ресурсу на сервере MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> хочет переключиться в режим {{mode}}", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> хочет переключиться в режим {{mode}}, потому что: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> переключился в режим {{mode}}", "didSwitchWithReason": "<PERSON><PERSON><PERSON> переключился в режим {{mode}}, потому что: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> хочет создать новую подзадачу в режиме {{mode}}:", "wantsToFinish": "<PERSON><PERSON><PERSON> хочет завершить эту подзадачу", "newTaskContent": "Инструкции по подзадаче", "completionContent": "Подзадача завершена", "resultContent": "Результаты подзадачи", "defaultResult": "Пожалуйста, переходите к следующей задаче.", "completionInstructions": "Подзадача завершена! Вы можете просмотреть результаты и предложить исправления или следующие шаги. Если всё в порядке, подтвердите для возврата результата в родительскую задачу."}, "questions": {"hasQuestion": "У Zhanlu есть вопрос:"}, "taskCompleted": "Задача завершена", "error": "Ошибка", "warning": "Предупреждение", "diffError": {"title": "Не удалось выполнить редактирование"}, "troubleMessage": "У Zhanlu возникли проблемы...", "powershell": {"issues": "Похоже, у вас проблемы с Windows PowerShell, пожалуйста, ознакомьтесь с этим"}, "autoApprove": {"title": "Автоодобрение:", "none": "Нет", "description": "Автоодобрение позволяет zhanlu выполнять действия без запроса разрешения. Включайте только для полностью доверенных действий. Более подробная настройка доступна в <settingsLink>Настройках</settingsLink>.", "selectOptionsFirst": "Выберите хотя бы один параметр ниже, чтобы включить автоодобрение", "toggleAriaLabel": "Переключить автоодобрение", "disabledAriaLabel": "Автоодобрение отключено - сначала выберите опции"}, "announcement": {"title": "🎉 Обновление Zhanlu версии 2.3.2", "description": "Исправления ошибок, автодополнение кода", "whatsNew": "Важные обновления", "feature1": "<bold>Добавлен режим Основы Кода</bold>: Режим Основы Кода генерирует упражнения по программированию", "feature2": "<bold>Исправление ошибки задач истории</bold>: Проблема решена, где новые задачи появляются в задачах истории", "feature3": "<bold>Исправление проблемы мерцания</bold>: Проблема случайного мерцания экрана при отображении кода решена", "feature4": "<bold>Другие оптимизации</bold>: Оптимизации различных других проблем", "hideButton": "Скрыть объявление", "detailsDiscussLinks": "Смотрите <discordLink>подробную документацию</discordLink>, чтобы узнать больше функций 🚀"}, "reasoning": {"thinking": "Обдумывание", "seconds": "{{count}}с"}, "contextCondense": {"title": "Контекст сжат", "condensing": "Сжатие контекста...", "errorHeader": "Не удалось сжать контекст", "tokens": "токены"}, "followUpSuggest": {"copyToInput": "Скопировать во ввод (то же, что shift + клик)", "autoSelectCountdown": "Автовыбор через {{count}}с", "countdownDisplay": "{{count}}с"}, "browser": {"rooWantsToUse": "<PERSON><PERSON><PERSON> хочет использовать браузер:", "consoleLogs": "Логи консоли", "noNewLogs": "(Новых логов нет)", "screenshot": "Скриншот браузера", "cursor": "курсор", "navigation": {"step": "Шаг {{current}} из {{total}}", "previous": "Предыдущий", "next": "Следующий"}, "sessionStarted": "Сессия браузера запущена", "actions": {"title": "Действие в браузере: ", "launch": "Открыть браузер по адресу {{url}}", "click": "Клик ({{coordinate}})", "type": "Ввести \"{{text}}\"", "scrollDown": "Прокрутить вниз", "scrollUp": "Прокрутить вверх", "close": "Закрыть браузер"}}, "codeblock": {"tooltips": {"expand": "Развернуть блок кода", "collapse": "Свернуть блок кода", "enable_wrap": "Включить перенос строк", "disable_wrap": "Отключить перенос строк", "copy_code": "Копировать код"}}, "qucikInstructions": {"UiToCode": "Код генерации рисунков UI", "UmlToCode": "Код генерации диаграмм UML", "ExplainCode": "Код интерпретации", "FixCode": "Исправление ошибок кода", "ImproveCode": "Оптимизация кода", "UnitTest": "Модульный тест", "CODE_REVIEW": "Крит<PERSON><PERSON><PERSON> кода", "CommentCode": "Комментарий кода", "PlusButtonClicked": "Открыть диалог"}, "systemPromptWarning": "ПРЕДУПРЕЖДЕНИЕ: Активна пользовательская системная подсказка. Это может серьезно нарушить работу и вызвать непредсказуемое поведение.", "profileViolationWarning": "Текущий профиль несовместим с настройками вашей организации", "shellIntegration": {"title": "Предупреждение о выполнении команды", "description": "Ваша команда выполняется без интеграции оболочки терминала VSCode. Чтобы скрыть это предупреждение, вы можете отключить интеграцию оболочки в разделе <strong>Terminal</strong> в <settingsLink>настройках Zhanlu</settingsLink> или устранить проблемы с интеграцией терминала VSCode, используя ссылку ниже.", "troubleshooting": "Нажмите здесь для просмотра документации по интеграции оболочки."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Достигнут лимит автоматически одобренных запросов", "description": "zhanlu достиг автоматически одобренного лимита в {{count}} API-запрос(ов). Хотите сбросить счетчик и продолжить задачу?", "button": "Сбросить и продолжить"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON><PERSON> хочет выполнить поиск в кодовой базе по <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> хочет выполнить поиск в кодовой базе по <code>{{query}}</code> в <code>{{path}}</code>:", "didSearch_one": "Найден 1 результат", "didSearch_other": "Найдено {{count}} результатов", "resultTooltip": "Оценка схожести: {{score}} (нажмите, чтобы открыть файл)"}, "read-batch": {"approve": {"title": "Одобрить все"}, "deny": {"title": "Отклонить все"}}, "indexingStatus": {"ready": "Индекс готов", "indexing": "Индексация {{percentage}}%", "indexed": "Проиндексировано", "error": "Ошибка индекса", "status": "Статус индекса"}, "versionIndicator": {"ariaLabel": "Версия {{version}} - Нажмите, чтобы просмотреть примечания к выпуску"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> Cloud скоро появится!", "description": "Запускайте удаленные агенты в облаке, получайте доступ к своим задачам из любого места, сотрудничайте с другими и многое другое.", "joinWaitlist": "Присоединитесь к списку ожидания для получения раннего доступа."}, "editMessage": {"placeholder": "Редактировать сообщение..."}}