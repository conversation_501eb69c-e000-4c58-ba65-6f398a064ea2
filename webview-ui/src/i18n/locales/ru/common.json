{"errorBoundary": {"title": "Что-то пошло не так", "reportText": "Пожалуйста, помогите нам улучшить приложение, сообщив об этой ошибке на", "githubText": "нашей странице GitHub Issues", "copyInstructions": "Скопируйте и вставьте следующее сообщение об ошибке, чтобы включить его в ваше сообщение:", "errorStack": "Стек ошибки:", "componentStack": "Стек компонентов:"}, "number_format": {"thousand_suffix": "тыс", "million_suffix": "млн", "billion_suffix": "млрд"}, "answers": {"yes": "Да", "no": "Нет", "cancel": "Отмена", "remove": "Удалить", "keep": "Оставить"}, "ui": {"search_placeholder": "Поиск..."}, "mermaid": {"loading": "Создание диаграммы mermaid...", "render_error": "Не удалось отобразить диаграмму", "file_media": "Предварительный просмотр процесса", "code": "Показать исходный код", "buttons": {"zoom": "Масш<PERSON><PERSON><PERSON>", "zoomIn": "Увеличить", "zoomOut": "Уменьшить", "copy": "Копировать", "save": "Сохранить изображение", "viewCode": "Посмотреть код", "viewDiagram": "Посмотреть диаграмму", "close": "Закрыть"}, "modal": {"codeTitle": "<PERSON><PERSON><PERSON> Me<PERSON>"}, "tabs": {"diagram": "Диаграмма", "code": "<PERSON>од"}, "feedback": {"imageCopied": "Изображение скопировано в буфер обмена", "copyError": "Ошибка копирования изображения"}}, "file": {"errors": {"invalidDataUri": "Неверный формат URI данных", "copyingImage": "Ошибка копирования изображения: {{error}}", "openingImage": "Ошибка открытия изображения: {{error}}", "pathNotExists": "Путь не существует: {{path}}", "couldNotOpen": "Не удалось открыть файл: {{error}}", "couldNotOpenGeneric": "Не удалось открыть файл!"}, "success": {"imageDataUriCopied": "URI данных изображения скопирован в буфер обмена"}}, "confirmation": {"deleteMessage": "Удалить Сообщение", "deleteWarning": "Удаление этого сообщения приведет к удалению всех последующих сообщений в разговоре. Хотите продолжить?", "editMessage": "Редактировать Сообщение", "editWarning": "Редактирование этого сообщения приведет к удалению всех последующих сообщений в разговоре. Хотите продолжить?", "proceed": "Продолжить"}}