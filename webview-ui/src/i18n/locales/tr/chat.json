{"greeting": "Zhan<PERSON>'<PERSON> Hoş Geldiniz", "task": {"title": "<PERSON><PERSON><PERSON><PERSON>", "seeMore": "<PERSON><PERSON> fazla gör", "seeLess": "<PERSON><PERSON> az gör", "tokens": "Tokenlar:", "cache": "Önbellek:", "apiCost": "API Maliyeti:", "contextWindow": "Bağlam Uzunluğu:", "closeAndStart": "<PERSON><PERSON><PERSON><PERSON> kapat ve yeni bir görev ba<PERSON><PERSON>", "export": "Görev geçmişini dışa aktar", "delete": "G<PERSON><PERSON>vi sil (Onayı atlamak için Shift + Tıkla)", "condenseContext": "Bağlamı akıllıca yoğunlaştır", "share": "<PERSON><PERSON><PERSON><PERSON>", "copyId": "Görev ID'sini kopyala", "shareWithOrganization": "Kuruluşla <PERSON>", "shareWithOrganizationDescription": "Sadece k<PERSON>şunuzun üyeleri erişebilir", "sharePublicly": "Herkese açık <PERSON>ş", "sharePubliclyDescription": "Bağlantıya sahip herkes erişebilir", "connectToCloud": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "connectToCloudDescription": "Görevleri paylaşmak için z<PERSON>'a giriş yap", "sharingDisabledByOrganization": "Paylaşım kuruluş tarafından devre dışı bırakıldı", "shareSuccessOrganization": "Organizasyon bağlantısı panoya kopyalandı", "shareSuccessPublic": "Genel bağlantı panoya kopyalandı"}, "unpin": "Sabitlemeyi iptal et", "pin": "<PERSON><PERSON><PERSON>", "tokenProgress": {"availableSpace": "Kullanılabil<PERSON> alan: {{amount}} token", "tokensUsed": "Kullanılan tokenlar: {{used}} / {{total}}", "reservedForResponse": "Model yanıtı için a<PERSON>ı<PERSON>: {{amount}} token"}, "retry": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "İşlemi tekrar dene"}, "startNewTask": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> bir g<PERSON><PERSON>v ba<PERSON><PERSON>"}, "proceedAnyways": {"title": "<PERSON><PERSON>", "tooltip": "Komut çalışırken devam et"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON>lerini kaydet"}, "reject": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "completeSubtaskAndReturn": "<PERSON> görevi tama<PERSON>la ve geri dön", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON> e<PERSON><PERSON> on<PERSON>la"}, "runCommand": {"title": "Komutu Çalıştır", "tooltip": "Bu komutu ç<PERSON>ıştır"}, "proceedWhileRunning": {"title": "Çalışırken Devam Et", "tooltip": "Uyarılara rağmen devam et"}, "killCommand": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Mevcut komutu durdur"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Mevcut göreve devam et"}, "terminate": {"title": "Sonlandır", "tooltip": "Mevcut görevi <PERSON>"}, "cancel": {"title": "İptal", "tooltip": "Mevcut işlemi iptal et"}, "scrollToBottom": "<PERSON><PERSON><PERSON><PERSON> altına kaydır", "about": "Zhanlu AI Modeli yardımı<PERSON> kod <PERSON>, <PERSON><PERSON><PERSON><PERSON>, ye<PERSON><PERSON> düzenleyin ve hata ayıklayın.<br />Daha fazla bilgi için <DocsLink>belgelerimize</DocsLink> göz atın.", "onboarding": "Bu çalışma alanındaki görev listesi boş. Aşağıya görevinizi girerek başlayın.<br>Nasıl başlayacağınızdan emin değil misiniz? <DocsLink>belgelerimizde</DocsLink> daha fazlasını okuyun.", "zhanluTips": {"boomerangTasks": {"title": "Görev Orkestrasyonu", "description": "Görevleri da<PERSON>, yönetilebilir par<PERSON>a a<PERSON>ırın."}, "stickyModels": {"title": "Yapışkan Modlar", "description": "Her mod, en son kull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modeli hatırlar"}, "tools": {"title": "Araçlar", "description": "AI'nın web'e göz atarak, komutlar çalıştırarak ve daha fazlasını yaparak sorunları çözmesine izin verin."}, "customizableModes": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ları ve atanmış modelleri ile özelleştirilmiş kişilikler"}, "architect": {"title": "<PERSON><PERSON>", "description": "Sistem mimarisi planlama ve kod yapısı tasarlama."}, "code": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON>, değiştirme ve optimize etme."}, "unit_test": {"title": "Birim Test Modu", "description": "Kod kararlılığını sağlamak için kapsamlı testler oluşturma."}, "project_fix": {"title": "<PERSON><PERSON>", "description": "Projedeki sorun veya hataları tespit etme ve düzeltme."}, "security_fix": {"title": "Güvenlik Düzeltme Modu", "description": "Koddaki güvenlik açıklarını tespit etme ve çözme."}, "code_review": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> kalitesini analiz etme ve iyileştirme önerileri sunma."}, "documentation": {"title": "Belgelendir<PERSON>", "description": "Net ve kapsamlı belgeler ve açıklamalar oluşturma."}, "qa": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Soruları yanıtlama ve basit yardım sağlama."}}, "selectMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> modunu se<PERSON>", "selectApiConfig": "API yapılandırmasını seçin", "internetSearch": "İnternet aramasını açtıktan sonra internette ilgili içerikleri arayabilirsiniz", "internetSearchClosed": "İnternet aramasını kapat", "enhancePrompt": "Ek bağ<PERSON>la istemi gel<PERSON>ştir", "addImages": "<PERSON>ja resim ekle", "sendMessage": "<PERSON><PERSON>", "stopTts": "<PERSON><PERSON> du<PERSON>", "typeMessage": "Bir mesaj yazın...", "typeTask": "Görevinizi buraya yazın...", "addContext": " @ ba<PERSON><PERSON><PERSON> ekle, / modu geçir, # kısa talimat", "dragFiles": "Shift tuşunu basılı tutup dosyaları sürükleyin", "dragFilesImages": "Shift tuşunu basılı tutup dosya/resi<PERSON><PERSON> s<PERSON>", "enhancePromptDescription": "'İstemi geli<PERSON>' <PERSON><PERSON><PERSON><PERSON><PERSON>, ek b<PERSON><PERSON><PERSON>, açıklama veya yeniden ifade sağlayarak isteğinizi iyileştirmeye yardımcı olur. Buraya bir istek yazıp düğmeye tekrar tıklayarak nasıl çalıştığını görebilirsiniz.", "modeSelector": {"title": "<PERSON><PERSON><PERSON>", "marketplace": "<PERSON><PERSON>", "settings": "<PERSON><PERSON>", "description": "Roo'nun davranışını özelleştiren uzmanlaşmış kişilikler."}, "errorReadingFile": "<PERSON><PERSON>a okuma hatası:", "noValidImages": "Hiçbir geçerli resim işlenmedi", "separator": "Ayırıcı", "edit": "Düzenle...", "forNextMode": "<PERSON><PERSON><PERSON> mod i<PERSON>in", "forPreviousMode": "önceki mod için", "error": "<PERSON><PERSON>", "warning": "Uyarı", "diffError": {"title": "Düzenleme Başarısız"}, "troubleMessage": "<PERSON><PERSON><PERSON> sorun yaşıyor...", "apiRequest": {"title": "API İsteği", "failed": "API İsteği Başarısız", "streaming": "API İsteği...", "cancelled": "API İsteği İptal Edildi", "streamingFailed": "API Akışı Başarısız"}, "checkpoint": {"initial": "İlk Kontrol Noktası", "regular": "<PERSON><PERSON><PERSON>", "initializingWarning": "Kontrol noktası hala başlatılıyor... Bu çok uzun sürerse, <settingsLink>ayarlar</settingsLink> bölümünden kontrol noktalarını devre dışı bırakabilir ve görevinizi yeniden başlatabilirsiniz.", "menu": {"viewDiff": "Farkları Görüntüle", "restore": "Kontrol Noktasını Geri <PERSON>", "restoreFiles": "Dosyaları Geri <PERSON>", "restoreFilesDescription": "Projenizin dosyalarını bu noktada alınan bir anlık görüntüye geri yükler.", "restoreFilesAndTask": "Dosyaları ve Görevi Geri <PERSON>", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "cannotUndo": "Bu işlem geri alınamaz.", "restoreFilesAndTaskDescription": "Projenizin dosyalarını bu noktada alınan bir anlık görüntüye geri yükler ve bu noktadan sonraki tüm mesajları siler."}, "current": "Mevcut"}, "instructions": {"wantsToFetch": "Zhanlu mevcut göreve yardımcı olmak için ayrıntılı talimatlar almak istiyor"}, "fileOperations": {"wantsToRead": "Zhanlu bu dosyayı okumak istiyor:", "wantsToReadOutsideWorkspace": "Zhanlu çalışma alanı dışındaki bu dosyayı okumak istiyor:", "didRead": "Zhanlu bu dosyayı okudu:", "wantsToEdit": "Zhanlu bu dosyayı düzenlemek istiyor:", "wantsToEditOutsideWorkspace": "Zhanlu çalışma alanı dışındaki bu dosyayı düzenlemek istiyor:", "wantsToEditProtected": "Zhanlu korumalı bir yapılandırma dosyasını düzenlemek istiyor:", "wantsToCreate": "Z<PERSON><PERSON> yeni bir dosya oluşturmak istiyor:", "wantsToSearchReplace": "<PERSON><PERSON><PERSON> bu dosyada arama ve değiştirme yapmak istiyor:", "didSearchReplace": "<PERSON><PERSON><PERSON> bu dosyada arama ve değiştirme yaptı:", "wantsToInsert": "Zhanlu bu dosyaya içerik eklemek istiyor:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> bu dosyanın {{lineNumber}}. satırına içerik eklemek istiyor:", "wantsToInsertAtEnd": "Zhanlu bu dosyanın sonuna içerik eklemek istiyor:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> bu dosyayı ve {{count}} tane daha okumak istiyor:", "wantsToReadMultiple": "<PERSON><PERSON><PERSON> birden fazla dosya okumak istiyor:", "wantsToApplyBatchChanges": "Zhanlu birden fazla dosyaya değişiklik uygulamak istiyor:"}, "directoryOperations": {"wantsToViewTopLevel": "Zhanlu bu dizindeki üst düzey dosyaları görüntülemek istiyor:", "didViewTopLevel": "Z<PERSON><PERSON> bu dizindeki üst düzey dosyaları görüntüledi:", "wantsToViewRecursive": "Zhanlu bu dizindeki tüm dosyaları özyinelemeli olarak görüntülemek istiyor:", "didViewRecursive": "Zhanlu bu dizindeki tüm dosyaları özyinelemeli olarak görüntüledi:", "wantsToViewDefinitions": "Z<PERSON>lu bu dizinde kullanılan kaynak kod tanımlama isimlerini görüntülemek istiyor:", "didViewDefinitions": "Z<PERSON>lu bu dizinde kullanılan kaynak kod tanımlama isimlerini görüntüledi:", "wantsToSearch": "<PERSON><PERSON><PERSON> bu dizinde <code>{{regex}}</code> i<PERSON><PERSON> arama yapmak istiyor:", "didSearch": "<PERSON><PERSON><PERSON> bu dizinde <code>{{regex}}</code> i<PERSON>in arama yaptı:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> bu dizinde (çalışma alanı dışında) <code>{{regex}}</code> i<PERSON><PERSON> arama yapmak istiyor:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> bu dizinde (çalışma alanı dışında) <code>{{regex}}</code> i<PERSON>in arama yaptı:", "wantsToViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> bu dizin<PERSON><PERSON> (çalışma alanı dışında) üst düzey dosyaları görüntülemek istiyor:", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> bu dizin<PERSON><PERSON> (çalışma alanı dışında) üst düzey dosyaları görüntüledi:", "wantsToViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> bu dizin<PERSON><PERSON> (çalışma alanı dışında) tüm dosyaları özyinelemeli olarak görüntülemek istiyor:", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> bu dizin<PERSON><PERSON> (çalışma alanı dışında) tüm dosyaları özyinelemeli olarak görüntüledi:", "wantsToViewDefinitionsOutsideWorkspace": "Z<PERSON><PERSON> bu dizinde (çalışma alanı dışında) kullanılan kaynak kod tanımlama isimlerini görüntülemek istiyor:", "didViewDefinitionsOutsideWorkspace": "Z<PERSON><PERSON> bu dizinde (çalışma alanı dışında) kullanılan kaynak kod tanımlama isimlerini görüntüledi:"}, "commandOutput": "Komut Çıktısı", "commandExecution": {"running": "Çalışıyor", "pid": "PID: {{pid}}", "exited": "Çıkıldı ({{exitCode}})", "manageCommands": "<PERSON><PERSON><PERSON>önet", "commandManagementDescription": "Komut izinlerini yönetin: Otomatik yürütmeye izin vermek için ✓'e, y<PERSON>rütmeyi reddetmek için ✗'e tıklayın. Desenler açılıp kapatılabilir veya listelerden kaldırılabilir. <settingsLink>Tüm ayarları görüntüle</settingsLink>", "addToAllowed": "İzin verilenler listesine ekle", "removeFromAllowed": "İzin verilenler listesinden kaldır", "addToDenied": "Reddedilenler listesine ekle", "removeFromDenied": "Reddedilenler listesinden kaldır", "abortCommand": "<PERSON><PERSON><PERSON> yü<PERSON><PERSON><PERSON>yi iptal et", "expandOutput": "Çıktıyı genişlet", "collapseOutput": "Çıktıyı daralt", "expandManagement": "<PERSON><PERSON><PERSON> yö<PERSON><PERSON> bölümünü g<PERSON>ş<PERSON>", "collapseManagement": "<PERSON><PERSON><PERSON> y<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>t"}, "response": "Yan<PERSON>t", "arguments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mcp": {"wantsToUseTool": "Z<PERSON>lu {{serverName}} MCP sunucusunda bir araç kullanmak istiyor:", "wantsToAccessResource": "Zhanlu {{serverName}} MCP sunucusundaki bir kaynağa erişmek istiyor:"}, "modes": {"wantsToSwitch": "Zhanlu <code>{{mode}}</code> moduna geçmek istiyor", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> <code>{{mode}}</code> moduna geçmek istiyor <PERSON>: {{reason}}", "didSwitch": "Z<PERSON>lu <code>{{mode}}</code> moduna geçti", "didSwitchWithReason": "<PERSON><PERSON><PERSON> <code>{{mode}}</code> moduna geç<PERSON>: {{reason}}"}, "subtasks": {"wantsToCreate": "Zhanlu <code>{{mode}}</code> modunda yeni bir alt görev oluşturmak istiyor:", "wantsToFinish": "<PERSON><PERSON><PERSON> bu alt görevi bitirmek istiyor", "newTaskContent": "Alt Görev Talimatları", "completionContent": "Alt Görev Tamamlandı", "resultContent": "Alt Görev Sonuçları", "defaultResult": "Lütfen sonraki göreve devam edin.", "completionInstructions": "Alt görev tamamlandı! Sonuçları inceleyebilir ve düzeltmeler veya sonraki adımlar önerebilirsiniz. Her şey iyi görünü<PERSON><PERSON>, sonucu üst göreve döndürmek için on<PERSON>ın."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON>'nun bir sorusu var:"}, "taskCompleted": "Görev Tamamlandı", "powershell": {"issues": "Windows PowerShell ile ilgili sorunlar yaşıyor gibi görünüyorsunuz, lütfen şu konuya bakın"}, "autoApprove": {"title": "Otomatik-onay:", "none": "Hiç<PERSON>i", "description": "Otomatik onay, zhanlu'un izin istemeden işlemler gerçekleştirmesine olanak tanır. Yalnızca tamamen güvendiğiniz eylemler için etkinleştirin. Daha detaylı yapılandırma <settingsLink>Ayarlar</settingsLink>'da mevcuttur.", "selectOptionsFirst": "Otomatik onayı etkinleştirmek için aşağıdan en az bir seçenek belirleyin", "toggleAriaLabel": "Otomatik onayı değiştir", "disabledAriaLabel": "Otomatik onay devre dışı - önce seçenekleri belirleyin"}, "reasoning": {"thinking": "Düşünüyor", "seconds": "{{count}}sn"}, "contextCondense": {"title": "Bağlam Özetlendi", "condensing": "Bağlam yoğunlaştırılıyor...", "errorHeader": "Bağlam yoğunlaştırılamadı", "tokens": "token"}, "followUpSuggest": {"copyToInput": "<PERSON><PERSON><PERSON> k<PERSON> (veya Shift + tıklama)", "autoSelectCountdown": "{{count}}s iç<PERSON>e otomatik seçilecek", "countdownDisplay": "{{count}}sn"}, "announcement": {"title": "🎉 <PERSON>hanlu 2.3.2 <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>, kod tama<PERSON>a", "whatsNew": "<PERSON><PERSON><PERSON><PERSON>", "feature1": "<bold>Kod <PERSON></bold>: Kod <PERSON><PERSON>lama alıştırmaları oluşturur", "feature2": "<bold>Geçmiş görev hatası düzeltmesi</bold>: <PERSON>ni görevlerin geçmiş görevlerde görünme sorunu ç<PERSON>", "feature3": "<bold>Tit<PERSON><PERSON><PERSON> sorun<PERSON> d<PERSON></bold>: Kod gösterirken ara sıra oluşan ekran titreşimi sorunu <PERSON>", "feature4": "<bold><PERSON><PERSON><PERSON> optimi<PERSON></bold>: <PERSON><PERSON><PERSON><PERSON><PERSON> di<PERSON><PERSON> sorun<PERSON>ın optimizasyonları", "hideButton": "<PERSON><PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Daha fazla özellik öğrenmek için <discordLink>ayrıntılı belgeleri</discordLink> kontrol edin 🚀"}, "browser": {"rooWantsToUse": "Zhanlu tarayıcıyı kullanmak istiyor:", "consoleLogs": "Konsol Kayıtları", "noNewLogs": "(<PERSON><PERSON> kayıt yok)", "screenshot": "Tarayıcı ekran görüntüsü", "cursor": "imleç", "navigation": {"step": "Adım {{current}} / {{total}}", "previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>"}, "sessionStarted": "Tarayıcı Oturumu Başlatıldı", "actions": {"title": "Tarayıcı İşlemi: ", "launch": "{{url}} ad<PERSON><PERSON><PERSON> ba<PERSON>lat", "click": "<PERSON><PERSON><PERSON> ({{coordinate}})", "type": "Yaz \"{{text}}\"", "scrollDown": "Aşağı kaydır", "scrollUp": "Yukarı kaydır", "close": "Tarayıcıyı kapat"}}, "codeblock": {"tooltips": {"expand": "<PERSON><PERSON> b<PERSON><PERSON><PERSON> g<PERSON>", "collapse": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "enable_wrap": "Sa<PERSON>ır kaydırmayı etkinleştir", "disable_wrap": "<PERSON><PERSON><PERSON>r kaydırmayı devre dışı bırak", "copy_code": "Kodu k<PERSON>ala"}}, "qucikInstructions": {"UiToCode": "UI tasarımı kod oluşturma", "UmlToCode": "UML grafik oluşturma kodu", "ExplainCode": "Kod Açıklaması", "FixCode": "Kod hatası", "ImproveCode": "<PERSON><PERSON>", "UnitTest": "<PERSON><PERSON><PERSON>", "CODE_REVIEW": "<PERSON><PERSON>", "CommentCode": "<PERSON><PERSON> yorumu", "PlusButtonClicked": "İletişimi b<PERSON>ın"}, "systemPromptWarning": "UYARI: <PERSON>zel sistem komut geçersiz kılma aktif. Bu işlevselliği ciddi şekilde bozabilir ve öngörülemeyen davranışlara neden olabilir.", "profileViolationWarning": "Geçerli profil kuruluşunuzun ayarlarıyla uyumlu değil", "shellIntegration": {"title": "Komut Çalıştırma Uyarısı", "description": "Komutunuz VSCode terminal kabuk entegrasyonu olmadan çalıştırılıyor. Bu uyarıyı gizlemek için <settingsLink>Z<PERSON><PERSON> a<PERSON>ları</settingsLink>'nın <strong>Terminal</strong> bölümünden kabuk entegrasyonunu devre dışı bırakabilir veya aşağıdaki bağlantıyı kullanarak VSCode terminal entegrasyonu sorunlarını giderebilirsiniz.", "troubleshooting": "Kabuk entegrasyonu belgelerini görmek için buraya tıklayın."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Otomatik Onaylanan İstek Limiti Aşıldı", "description": "<PERSON>oo, {{count}} API isteği/istekleri için otomatik onaylanan limite ulaştı. Sayacı sıfırlamak ve göreve devam etmek istiyor musunuz?", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve Devam Et"}}, "codebaseSearch": {"wantsToSearch": "z<PERSON><PERSON> kod tabanında <code>{{query}}</code> aramak istiyor:", "wantsToSearchWithPath": "z<PERSON><PERSON> <code>{{path}}</code> içinde kod tabanında <code>{{query}}</code> aramak istiyor:", "didSearch_one": "1 sonu<PERSON> bulundu", "didSearch_other": "{{count}} <PERSON><PERSON><PERSON> bulundu", "resultTooltip": "Benzerlik puanı: {{score}} (dosyayı açmak için tıklayın)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "deny": {"title": "Tümünü Reddet"}}, "indexingStatus": {"ready": "İndeks hazır", "indexing": "İndeksleniyor {{percentage}}%", "indexed": "İndekslendi", "error": "İndeks hatası", "status": "İndeks durumu"}, "versionIndicator": {"ariaLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{version}} - <PERSON><PERSON><PERSON><PERSON><PERSON> notlarını görüntülemek için tıklayın"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> Cloud yakında geliyor!", "description": "Bulutta uzak ajan<PERSON>ış<PERSON>ırı<PERSON>, g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> her yerden er<PERSON>, başkalar<PERSON>yla işbirliği yapın ve daha fazlası.", "joinWaitlist": "<PERSON><PERSON><PERSON> er<PERSON><PERSON><PERSON> i<PERSON> bekleme listesine katılın."}, "editMessage": {"placeholder": "Mesajını düzenle..."}}