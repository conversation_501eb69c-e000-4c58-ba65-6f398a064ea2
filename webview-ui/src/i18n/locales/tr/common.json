{"errorBoundary": {"title": "Bir şeyler yanlış gitti", "reportText": "Bu hatayı bildirerek iyileştirmemize yardımcı olun", "githubText": "GitHub Issues sayfamızda", "copyInstructions": "Gönderiminize dahil etmek için a<PERSON>ağıdaki hata mesajını kopyalayıp yapıştırın:", "errorStack": "Hata Yığını:", "componentStack": "Bileşen Yığını:"}, "answers": {"yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "cancel": "İptal", "remove": "Kaldır", "keep": "<PERSON><PERSON>"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "Ara..."}, "mermaid": {"loading": "Mermaid diyagramı oluşturuluyor...", "render_error": "Diyagram render edilemiyor", "file_media": "<PERSON><PERSON><PERSON><PERSON>", "code": "Kaynak Ko<PERSON> Göster", "buttons": {"zoom": "Yakınlaştır", "zoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copy": "Kopyala", "save": "<PERSON><PERSON><PERSON>", "viewCode": "<PERSON><PERSON>", "viewDiagram": "Diyagramı görüntüle", "close": "Ka<PERSON><PERSON>"}, "modal": {"codeTitle": "Mermaid Kodu"}, "tabs": {"diagram": "Diyagram", "code": "Kod"}, "feedback": {"imageCopied": "Görsel panoya kopyalandı", "copyError": "G<PERSON><PERSON>l kopyalama hatası"}}, "file": {"errors": {"invalidDataUri": "Geçersiz veri URI formatı", "copyingImage": "<PERSON><PERSON><PERSON><PERSON> kopyalama hatası: {{error}}", "openingImage": "<PERSON><PERSON><PERSON><PERSON> açma hatası: {{error}}", "pathNotExists": "Yol mevcut değil: {{path}}", "couldNotOpen": "<PERSON><PERSON><PERSON> a<PERSON>adı: {{error}}", "couldNotOpenGeneric": "Dosya açılamadı!"}, "success": {"imageDataUriCopied": "Görsel veri URI'si panoya kopyalandı"}}, "confirmation": {"deleteMessage": "Mesajı Sil", "deleteWarning": "<PERSON>u mesajı silmek, konuşmadaki sonraki tüm mesajları da silecektir. <PERSON><PERSON> etmek istiyor musun?", "editMessage": "Mesajı <PERSON>", "editWarning": "<PERSON>u mesajı d<PERSON>, konuşmadaki sonraki tüm mesajları da silecektir. <PERSON><PERSON> etmek istiyor musun?", "proceed": "<PERSON><PERSON>"}}