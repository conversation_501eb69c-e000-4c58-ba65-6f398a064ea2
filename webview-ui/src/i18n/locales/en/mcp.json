{"title": "MCP Servers", "done": "Done", "marketplace": "MCP Marketplace", "description": "The <0>Model Context Protocol</0> enables communication with locally running MCP servers that provide additional tools and resources to extend Roo's capabilities. You can use <1>community-made servers</1> or ask <PERSON><PERSON><PERSON> to create new tools specific to your workflow (e.g., \"add a tool that gets the latest npm docs\").", "instructions": "Instructions", "enableToggle": {"title": "Enable MCP Servers", "description": "When enabled, <PERSON><PERSON><PERSON> will be able to interact with MCP servers for advanced functionality. If you're not using MCP, you can disable this to reduce <PERSON><PERSON><PERSON>'s token usage."}, "enableServerCreation": {"title": "Enable MCP Server Creation", "description": "When enabled, <PERSON><PERSON><PERSON> can help you create new MCP servers via commands like \"add a new tool to...\". If you don't need to create MCP servers you can disable this to reduce <PERSON><PERSON><PERSON>'s token usage.", "hint": "Hint: To reduce API token costs, disable this setting when you are not actively asking <PERSON><PERSON><PERSON> to create a new MCP server."}, "editGlobalMCP": "Edit Global MCP", "editProjectMCP": "Edit Project MCP", "whatIsMcp": "What is MCP Server", "refreshMCP": "Refresh MCP Servers", "learnMoreEditingSettings": "Learn more about editing MCP settings files", "tool": {"alwaysAllow": "Always allow", "parameters": "Parameters", "noDescription": "No description", "togglePromptInclusion": "Toggle inclusion in prompt"}, "tabs": {"tools": "Tools", "resources": "Resources", "errors": "Errors"}, "emptyState": {"noTools": "No tools found", "noResources": "No resources found", "noErrors": "No errors found"}, "networkTimeout": {"label": "Network Timeout", "description": "Maximum time to wait for server responses", "options": {"15seconds": "15 seconds", "30seconds": "30 seconds", "1minute": "1 minute", "5minutes": "5 minutes", "10minutes": "10 minutes", "15minutes": "15 minutes", "30minutes": "30 minutes", "60minutes": "60 minutes"}}, "deleteDialog": {"title": "Delete MCP Server", "description": "Are you sure you want to delete the MCP server \"{{serverName}}\"? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete"}, "serverStatus": {"retrying": "Retrying...", "retryConnection": "Retry Connection"}, "execution": {"running": "Running", "completed": "Completed", "error": "Error"}}