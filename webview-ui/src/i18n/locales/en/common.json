{"errorBoundary": {"title": "Something went wrong", "reportText": "Please help us improve by reporting this error on", "githubText": "our GitHub Issues page", "copyInstructions": "Copy and paste the following error message to include it as part of your submission:", "errorStack": "Erro<PERSON>:", "componentStack": "Component Stack:"}, "answers": {"yes": "Yes", "no": "No", "cancel": "Cancel", "remove": "Remove", "keep": "Keep"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "Search..."}, "mermaid": {"loading": "Generating mermaid diagram...", "render_error": "Unable to Render Diagram", "file_media": "Process Preview", "code": "Source code display", "buttons": {"zoom": "Zoom", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "copy": "Copy", "save": "Save Image", "viewCode": "View Code", "viewDiagram": "View Diagram", "close": "Close"}, "modal": {"codeTitle": "Mermaid Code"}, "tabs": {"diagram": "Diagram", "code": "Code"}, "feedback": {"imageCopied": "Image copied to clipboard", "copyError": "Error copying image"}}, "file": {"errors": {"invalidDataUri": "Invalid data URI format", "copyingImage": "Error copying image: {{error}}", "openingImage": "Error opening image: {{error}}", "pathNotExists": "Path does not exist: {{path}}", "couldNotOpen": "Could not open file: {{error}}", "couldNotOpenGeneric": "Could not open file!"}, "success": {"imageDataUriCopied": "Image data URI copied to clipboard"}}, "confirmation": {"deleteMessage": "Delete Message", "deleteWarning": "Deleting this message will delete all subsequent messages in the conversation. Do you want to proceed?", "editMessage": "Edit Message", "editWarning": "Editing this message will delete all subsequent messages in the conversation. Do you want to proceed?", "proceed": "Proceed"}}