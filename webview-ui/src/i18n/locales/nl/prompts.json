{"title": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "modes": {"title": "<PERSON><PERSON>", "createNewMode": "Nieuwe modus aanmaken", "importMode": "Modus importeren", "noMatchFound": "<PERSON><PERSON> modi gevonden", "editModesConfig": "Modusconfiguratie bewerken", "editGlobalModes": "Globale modi bewerken", "editProjectModes": "<PERSON><PERSON>di bewerken (.z<PERSON>)", "createModeHelpText": "<PERSON><PERSON> op + om een nieuwe aangepaste modus te maken, of v<PERSON><PERSON> in de chat om er een voor je te maken!", "selectMode": "Modus z<PERSON>"}, "apiConfiguration": {"title": "API-configuratie", "select": "Selecteer welke API-configuratie voor deze modus gebruikt moet worden"}, "tools": {"title": "Beschikbare tools", "builtInModesText": "Tools voor ingebouwde modi kunnen niet worden aangepast", "editTools": "Tools bewerken", "doneEditing": "Bewerken voltooid", "allowedFiles": "<PERSON><PERSON><PERSON><PERSON> best<PERSON>:", "toolNames": {"read": "<PERSON><PERSON><PERSON> le<PERSON>", "edit": "Bestanden bewerken", "browser": "Browser geb<PERSON>iken", "command": "Commando's u<PERSON><PERSON><PERSON><PERSON>", "mcp": "MCP gebruiken"}, "noTools": "<PERSON><PERSON>"}, "roleDefinition": {"title": "Roldefinitie", "resetToDefault": "Terugzetten naar standaard", "description": "<PERSON><PERSON><PERSON><PERSON>'s expertise en persoonlijkheid voor deze modus. Deze beschrijving bepaalt hoe <PERSON><PERSON> zich presenteert en taken benadert."}, "description": {"title": "<PERSON><PERSON> beschri<PERSON> (voor mensen)", "resetToDefault": "Terugzetten naar standaardbeschrijving", "description": "<PERSON>en korte beschrijving die wordt getoond in de modusselectie dropdown."}, "whenToUse": {"title": "<PERSON><PERSON> te geb<PERSON>ike<PERSON> (optioneel)", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> wanneer deze modus gebruikt moet worden. <PERSON><PERSON> helpt de Orchestrator om de juiste modus voor een taak te kiezen.", "resetToDefault": "Beschrijving '<PERSON><PERSON> te gebruiken' terugzetten naar standaard"}, "customInstructions": {"title": "Modusspecifieke instructies (optioneel)", "resetToDefault": "Terugzetten naar standaard", "description": "Voeg gedragsrichtlijnen toe die specifiek zijn voor de modus {{modeName}}.", "loadFromFile": "Modusspecifieke instructies voor {{mode}} kunnen ook worden geladen uit de map <span>.zhanlu/rules-{{slug}}/</span> in je werkruimte (.zhanlurules-{{slug}} en .clinerules-{{slug}} zijn verouderd en werken binnenkort niet meer)."}, "exportMode": {"title": "Modus exporteren", "description": "Exporteer deze modus naar een YAML-bestand met alle regels inbegrepen voor eenvoudig delen met and<PERSON><PERSON>.", "export": "Modus exporteren", "exporting": "Exporteren..."}, "importMode": {"selectLevel": "Kies waar je deze modus wilt importeren:", "import": "Importeren", "importing": "Importeren...", "global": {"label": "Globaal niveau", "description": "Besch<PERSON><PERSON>ar in alle projecten. Regels worden samengevoegd in aangepaste instructies."}, "project": {"label": "Projectniveau", "description": "<PERSON><PERSON> be<PERSON> in deze werkruimte. Als de geëxporteerde modus regelbestanden bevatte, worden deze opnieuw gemaakt in de map .zhanlu/rules-{slug}/."}}, "advanced": {"title": "Geavanceerd"}, "globalCustomInstructions": {"title": "Aangepaste instructies voor alle modi", "description": "Deze instructies gelden voor alle modi. Ze bieden een basisset aan gedragingen die kunnen worden uitgebreid met modusspecifieke instructies hieronder.\nWil je dat <PERSON> in een andere taal denkt en spreekt dan de weergavetaal van je editor ({{language}}), dan kun je dat hier aangeven.", "loadFromFile": "Instructies kunnen ook worden geladen uit de map <span>.zhanlu/rules/</span> in je werkruimte (.zhanlurules en .clinerules zijn verouderd en werken binnenkort niet meer)."}, "systemPrompt": {"preview": "Systeemp<PERSON><PERSON> be<PERSON>en", "copy": "Systeemprompt kopiëren naar klembord", "title": "Systeemprompt ({{modeName}} modus)"}, "supportPrompts": {"title": "Ondersteuningsprompts", "resetPrompt": "Reset {{promptType}} prompt naar standaard", "prompt": "Prompt", "enhance": {"apiConfiguration": "API-configuratie", "apiConfigDescription": "Je kunt een API-configuratie selecteren die altijd wordt gebruikt voor het verbeteren van prompts, of gewoon de huidige selectie gebruiken", "useCurrentConfig": "Huidige API-configuratie g<PERSON>n", "testPromptPlaceholder": "<PERSON><PERSON>r een prompt in om de verbetering te testen", "previewButton": "Voorbeeld promptverbetering", "testEnhancement": "Test verbetering"}, "condense": {"apiConfiguration": "API-configuratie voor contextcondensatie", "apiConfigDescription": "Selecteer welke API-configuratie moet worden gebruikt voor contextcondensatiebewerkingen. Laat leeg om de huidige actieve configuratie te gebruiken.", "useCurrentConfig": "Gebruik de momenteel geselecteerde API-configuratie"}, "types": {"ENHANCE": {"label": "Prompt verbeteren", "description": "Gebruik promptverbetering om op maat gemaakte suggesties of verbeteringen voor je invoer te krijgen. <PERSON><PERSON> begrij<PERSON> je intentie en krijg je de best mogelijke antwoorden. Beschikbaar via het ✨-icoon in de chat."}, "CONDENSE": {"label": "Contextcondensatie", "description": "Configureer hoe de gesprekscontext wordt gecondenseerd om tokenlimieten te beheren.Deze prompt wordt gebruikt voor zowel handmatige als automatische contextcondensatiebewerkingen."}, "EXPLAIN": {"label": "Code uitleggen", "description": "<PERSON><PERSON><PERSON><PERSON> gedetailleerde uitleg over codefragmenten, functies of hele bestanden. Handig om complexe code te begrijpen of nieuwe patronen te leren. Beschikbaar via codeacties (lampje in de editor) en het contextmenu (rechtsklik op geselecteerde code)."}, "FIX": {"label": "Problemen oplossen", "description": "Krijg hulp bij het identificeren en oplossen van bugs, fouten of codekwaliteitsproblemen. Biedt stapsgewijze begeleiding bij het oplossen van problemen. Beschikbaar via codeacties (lampje in de editor) en het contextmenu (rechtsklik op geselecteerde code)."}, "IMPROVE": {"label": "Code verbeteren", "description": "Ontvang suggesties voor codeoptimalisatie, betere praktijken en architecturale verbeteringen met behoud van functionaliteit. <PERSON>sch<PERSON><PERSON>ar via codeacties (lampje in de editor) en het contextmenu (rechtsklik op geselecteerde code)."}, "ADD_TO_CONTEXT": {"label": "Aan context toevoegen", "description": "Voeg context toe aan je huidige taak of gesprek. Handig voor extra informatie of verduidelijkingen. Beschikbaar via codeacties (lampje in de editor) en het contextmenu (rechtsklik op geselecteerde code)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Terminalinhoud aan context toevoegen", "description": "Voeg terminaluitvoer toe aan je huidige taak of gesprek. Handig voor commando-uitvoer of logboeken. Beschikbaar in het terminalcontextmenu (rechtsklik op geselecteerde terminalinhoud)."}, "TERMINAL_FIX": {"label": "Terminalcommando repareren", "description": "<PERSON><PERSON><PERSON><PERSON> hulp bij het repareren van terminalcommando's die zijn mislukt of verbetering nodig hebben. Beschikbaar in het terminalcontextmenu (rechtsklik op geselecteerde terminalinhoud)."}, "TERMINAL_EXPLAIN": {"label": "Terminalcommando uitleggen", "description": "K<PERSON><PERSON>g gedetailleerde uitleg over terminalcommando's en hun uitvoer. Beschikbaar in het terminalcontextmenu (rechtsklik op geselecteerde terminalinhoud)."}, "NEW_TASK": {"label": "<PERSON><PERSON><PERSON> taak starten", "description": "Start een ni<PERSON><PERSON> met geb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON> via de Command <PERSON>."}, "UNIT_TEST": {"label": "Unit test", "description": "Genereer uitgebreide unit tests voor de geselecteerde code. Beschikbaar via codeacties (lampje in de editor) en het contextmenu van de editor (rechtsklik op geselecteerde code)."}, "CODE_REVIEW": {"label": "Code review", "description": "Voer een gedetailleerde code review uit en geef verbeteringsuggesties voor de geselecteerde code. Beschikbaar via codeacties (lampje in de editor) en het contextmenu van de editor (rechtsklik op geselecteerde code)."}, "COMMENT_CODE": {"label": "Code commentaar", "description": "Voeg gedetailleerde commentaren en documentatie toe aan de geselecteerde code. Beschikbaar via codeacties (lampje in de editor) en het contextmenu van de editor (rechtsklik op geselecteerde code)."}}}, "advancedSystemPrompt": {"title": "Geavanceerd: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Je kunt de systeemprompt voor deze modus volledig vervangen (behalve de roldefinitie en aangepaste instructies) door een bestand aan te maken op <span>.zhanlu/system-prompt-{{slug}}</span> in je werkruimte. Dit is een zeer geavanceerde functie die ingebouwde beveiligingen en consistentiecontroles omzeilt (vooral rond toolgebruik), dus wees voorzichtig!"}, "createModeDialog": {"title": "Nieuwe modus aanmaken", "close": "Sluiten", "name": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> de na<PERSON> van de modus in", "tooLong": "<PERSON>am mag niet langer zijn dan 20 tekens"}, "slug": {"label": "Slug", "description": "De slug wordt gebruikt in URL's en bestandsnamen. Moet kleine letters, cijfers en koppeltekens bevatten.", "tooLong": "Slug mag niet langer zijn dan 20 tekens"}, "saveLocation": {"label": "Opslaglocatie", "description": "<PERSON><PERSON> waar je deze modus wilt opslaan. Projectspecifieke modi hebben voorrang op globale modi.", "global": {"label": "Globaal", "description": "Beschikbaar in alle werkruimtes"}, "project": {"label": "Projectspecifiek (.z<PERSON>)", "description": "<PERSON><PERSON> be<PERSON> in deze werkruimte, heeft voorrang op globaal"}}, "roleDefinition": {"label": "Roldefinitie", "description": "<PERSON><PERSON><PERSON><PERSON>'s expertise en persoonlijkheid voor deze modus."}, "tools": {"label": "Beschikbare tools", "description": "Selecteer welke tools deze modus kan gebruiken."}, "description": {"label": "<PERSON><PERSON> beschri<PERSON> (voor mensen)", "description": "<PERSON>en korte beschrijving die wordt getoond in de modusselectie dropdown."}, "customInstructions": {"label": "Aangepaste instructies (optioneel)", "description": "Voeg gedragsrichtlijnen toe die specifiek zijn voor deze modus."}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "create": "Modus aanmaken"}, "deleteMode": "Modus verwi<PERSON>en"}, "allFiles": "alle bestanden", "deleteMode": {"title": "Modus verwi<PERSON>en", "message": "Weet je zeker dat je de modus \"{{modeName}}\" wilt verwijderen?", "rulesFolder": "Deze modus heeft een regelmap op {{folderPath}} die ook wordt verwijderd.", "descriptionNoRules": "Weet je zeker dat je deze aangepaste modus wilt verwijderen?", "confirm": "Verwijderen", "cancel": "<PERSON><PERSON><PERSON>"}}