{"errorBoundary": {"title": "Er is iets misgegaan", "reportText": "Help ons te verbeteren door deze fout te melden op", "githubText": "onze GitHub Issues-pagina", "copyInstructions": "<PERSON><PERSON><PERSON> en plak het volgende foutbericht om het als onderdeel van je melding op te nemen:", "errorStack": "Foutstack:", "componentStack": "Componentstack:"}, "answers": {"yes": "<PERSON>a", "no": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Verwijderen", "keep": "Behouden"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "mrd"}, "ui": {"search_placeholder": "Zoeken..."}, "mermaid": {"loading": "Mermaid-diagram genereren...", "render_error": "Kan diagram niet weergeven", "file_media": "<PERSON><PERSON><PERSON><PERSON> van het proces", "code": "Broncode weergeven", "buttons": {"zoom": "Zoom", "zoomIn": "Inzoomen", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON><PERSON>", "save": "Afbeelding opslaan", "viewCode": "Code bekijken", "viewDiagram": "Diagram be<PERSON>jken", "close": "Sluiten"}, "modal": {"codeTitle": "Mermaid-code"}, "tabs": {"diagram": "Diagram", "code": "Code"}, "feedback": {"imageCopied": "Afbeelding gekopieerd naar klembord", "copyError": "Fout bij kopi<PERSON><PERSON> van a<PERSON>"}}, "file": {"errors": {"invalidDataUri": "Ongeldig data-URI-formaat", "copyingImage": "Fout bij kop<PERSON><PERSON><PERSON>: {{error}}", "openingImage": "Fout bij openen van afbeelding: {{error}}", "pathNotExists": "Pad bestaat niet: {{path}}", "couldNotOpen": "Kon bestand niet openen: {{error}}", "couldNotOpenGeneric": "Kon bestand niet openen!"}, "success": {"imageDataUriCopied": "Afbeelding data-URI gekopieerd naar klembord"}}, "confirmation": {"deleteMessage": "Bericht Verwijderen", "deleteWarning": "Het verwijderen van dit bericht zal alle volgende berichten in het gesprek verwijderen. Wil je doorgaan?", "editMessage": "Bericht Bewerken", "editWarning": "Het bewerken van dit bericht zal alle volgende berichten in het gesprek verwijderen. Wil je doorgaan?", "proceed": "Doorgaan"}}