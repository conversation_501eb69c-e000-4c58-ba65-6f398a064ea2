{"common": {"save": "Opsla<PERSON>", "done": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "reset": "Resetten", "select": "Selecteren", "add": "<PERSON><PERSON>", "remove": "Verwijderen"}, "header": {"title": "Instellingen", "saveButtonTooltip": "Wijzigingen opslaan", "nothingChangedTooltip": "Niets gewijzigd", "doneButtonTooltip": "Niet-opgeslagen wijzigingen negeren en instellingen sluiten"}, "unsavedChangesDialog": {"title": "Niet-opgeslagen wijzigingen", "description": "Wil je de wijzigingen negeren en doorgaan?", "cancelButton": "<PERSON><PERSON><PERSON>", "discardButton": "Wijzigingen negeren"}, "sections": {"providers": "Providers", "autoApprove": "Auto-goedkeuren", "browser": "Browser", "checkpoints": "Checkpoints", "notifications": "Meldingen", "contextManagement": "Context", "terminal": "Terminal", "prompts": "Prompts", "completion": "<PERSON><PERSON><PERSON><PERSON>", "experimental": "Experimenteel", "language": "Taal", "about": "<PERSON> zhan<PERSON>"}, "prompts": {"description": "Configureer ondersteuningsprompts die worden gebruikt voor snelle acties zoals het verbeteren van prompts, het uitleggen van code en het oplossen van problemen. Deze prompts helpen zhanlu om betere ondersteuning te bieden voor veelvoorkomende ontwikkelingstaken."}, "codeIndex": {"title": "Codebase indexering", "enableLabel": "Codebase indexering inschakelen", "enableDescription": "Code-indexering inschakelen voor verbeterde zoekresultaten en contextbegrip", "providerLabel": "Embeddings provider", "selectProviderPlaceholder": "Selecteer provider", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "API-sleutel:", "geminiApiKeyPlaceholder": "Voer uw Gemini API-sleutel in", "mistralProvider": "<PERSON><PERSON><PERSON>", "mistralApiKeyLabel": "API-sleutel:", "mistralApiKeyPlaceholder": "Voer uw Mistral API-sleutel in", "openaiCompatibleProvider": "OpenAI-compatibel", "openAiKeyLabel": "OpenAI API-sleutel", "openAiKeyPlaceholder": "Voer uw OpenAI API-sleutel in", "openAiCompatibleBaseUrlLabel": "Basis-URL", "openAiCompatibleApiKeyLabel": "API-sleutel", "openAiCompatibleApiKeyPlaceholder": "Voer uw API-sleutel in", "openAiCompatibleModelDimensionLabel": "Embedding Dimensie:", "modelDimensionLabel": "<PERSON> <PERSON><PERSON><PERSON>", "openAiCompatibleModelDimensionPlaceholder": "bijv., 1536", "openAiCompatibleModelDimensionDescription": "De embedding dimensie (uitvoergrootte) voor uw model. Controleer de documentatie van uw provider voor deze waarde. Veelvoorkomende waarden: 384, 768, 1536, 3072.", "modelLabel": "Model", "selectModelPlaceholder": "Selecteer model", "ollamaUrlLabel": "Ollama URL:", "qdrantUrlLabel": "Qdrant URL", "qdrantKeyLabel": "Qdrant-sleutel:", "startIndexingButton": "Start", "clearIndexDataButton": "Index wissen", "unsavedSettingsMessage": "Sla je instellingen op voordat je het indexeringsproces start.", "clearDataDialog": {"title": "Weet je het zeker?", "description": "Deze actie kan niet ongedaan worden gemaakt. Dit zal je codebase-indexgegevens permanent verwijderen.", "cancelButton": "<PERSON><PERSON><PERSON>", "confirmButton": "G<PERSON>vens wissen"}, "description": "Configureer codebase-indexeringsinstellingen om semantisch zoeken voor je project in te schakelen. <0>Meer informatie</0>", "statusTitle": "Status", "settingsTitle": "Indexeringsinstellingen", "disabledMessage": "Codebase-indexering is momenteel uitgeschakeld. <PERSON><PERSON><PERSON> het in de algemene instellingen in om indexeringsopties te configureren.", "embedderProviderLabel": "Embedder Provider", "modelPlaceholder": "<PERSON><PERSON><PERSON> in", "selectModel": "Selecteer een model", "ollamaBaseUrlLabel": "Ollama Basis-URL", "qdrantApiKeyLabel": "Qdrant API-sleutel", "qdrantApiKeyPlaceholder": "<PERSON><PERSON><PERSON> je Qdrant API-sleutel in (optioneel)", "setupConfigLabel": "Instellen", "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "Kan instellingen niet opslaan", "modelDimensions": "({{dimension}} dimensies)", "saveSuccess": "Instellingen succesvol opgeslagen", "saving": "Opslaan...", "saveSettings": "Opsla<PERSON>", "indexingStatuses": {"standby": "Stand-by", "indexing": "Indexeren", "indexed": "Geïndexeerd", "error": "Fout"}, "close": "Sluiten", "validation": {"invalidQdrantUrl": "Ongeldige Qdrant URL", "invalidOllamaUrl": "Ongeldige Ollama URL", "invalidBaseUrl": "Ongeldige basis-URL", "qdrantUrlRequired": "Qdrant URL is vereist", "openaiApiKeyRequired": "OpenAI API-sleutel is vereist", "modelSelectionRequired": "Modelselectie is vereist", "apiKeyRequired": "API-sleutel is vereist", "modelIdRequired": "Model-ID is vereist", "modelDimensionRequired": "Modelafmeting is vereist", "geminiApiKeyRequired": "Gemini API-sleutel is vereist", "mistralApiKeyRequired": "Mistral API-sleutel is vereist", "ollamaBaseUrlRequired": "Ollama basis-URL is vereist", "baseUrlRequired": "Basis-URL is vereist", "modelDimensionMinValue": "Modelafmeting moet groter zijn dan 0"}, "advancedConfigLabel": "Geavanceerde configuratie", "searchMinScoreLabel": "Zoekscore drempel", "searchMinScoreDescription": "Minimale overeenkomstscore (0.0-1.0) vereist voor zoekresultaten. Lagere waarden leveren meer resultaten op, maar zijn mogelijk minder relevant. Hogere waarden leveren minder, maar relevantere resultaten op.", "searchMinScoreResetTooltip": "Reset naar standaardwaarde (0.4)", "searchMaxResultsLabel": "Maximum Zoe<PERSON>aten", "searchMaxResultsDescription": "Maximum aantal zoekresultaten dat wordt geretourneerd bij het doorzoeken van de codebase-index. Hogere waarden bieden meer context maar kunnen minder relevante resultaten bevatten.", "resetToDefault": "<PERSON>set naar standaard"}, "autoApprove": {"description": "<PERSON><PERSON> <PERSON><PERSON><PERSON> toe om automatisch handelingen uit te voeren zonder goedkeuring. <PERSON><PERSON><PERSON> deze instellingen alleen in als je de AI volledig vertrouwt en de bijbehorende beveiligingsrisico's begrijpt.", "toggleAriaLabel": "Automatisch goedkeuren in-/uitschakelen", "disabledAriaLabel": "Automatisch goedkeuren uitgeschakeld - selecteer eerst opties", "readOnly": {"label": "<PERSON><PERSON>", "description": "Indien ingeschakeld, bekijkt zhanlu automatisch de inhoud van mappen en leest bestanden zonder dat je op de Goedkeuren-knop hoeft te klikken.", "outsideWorkspace": {"label": "Inclusief bestanden buiten werkruimte", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON> toe om bestanden buiten de huidige werkruimte te lezen zonder goedkeuring."}}, "write": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Automatisch bestanden aanmaken en bewerken zonder goedkeuring", "delayLabel": "Vertraging na schrijven om diagnostiek de kans te geven mogelijke problemen te detecteren", "outsideWorkspace": {"label": "Inclusief bestanden buiten werkruimte", "description": "<PERSON><PERSON> <PERSON><PERSON><PERSON> toe om bestanden buiten de huidige werkruimte aan te maken en te bewerken zonder goedkeuring."}, "protected": {"label": "Inclusief beschermde bestanden", "description": "<PERSON><PERSON> zhan<PERSON> toe om beschermde bestanden (zoals .zhanluignore en .zhanlu/ configuratiebestanden) aan te maken en te bewerken zonder goedkeuring."}}, "browser": {"label": "Browser", "description": "Automatisch browseracties uitvoeren zonder goedkeuring. Let op: geldt alleen als het model computergebruik ondersteunt."}, "retry": {"label": "Opnieuw proberen", "description": "Automatisch mislukte API-verzoeken opnieuw proberen wanneer de server een foutmelding geeft", "delayLabel": "Vertraging voordat het verzoek opnieuw wordt geprobeerd"}, "mcp": {"label": "MCP", "description": "Automatische goedkeuring van individuele MCP-tools in het MCP-serversoverzicht inschakelen (vereist zowel deze instelling als het selectievakje 'Altijd toestaan' bij de tool)"}, "modeSwitch": {"label": "Modus", "description": "Automatisch tussen verschillende modi schakelen zonder goedkeuring"}, "subtasks": {"label": "Subtaken", "description": "Subtaken aanmaken en afronden zonder goedkeuring"}, "followupQuestions": {"label": "Vraag", "description": "Selecteer automatisch het eerste voorgestelde antwoord voor vervolgvragen na de geconfigureerde time-out", "timeoutLabel": "Wachttijd voordat het eerste antwoord automatisch wordt geselecteerd"}, "execute": {"label": "Uitvoeren", "description": "Automatisch toegestane terminalcommando's uitvoeren zonder goedkeuring", "allowedCommands": "Toegestane automatisch uit te voeren commando's", "allowedCommandsDescription": "Commando-prefixen die automatisch kunnen worden uitgevoerd als 'Altijd goedkeuren voor uitvoeren' is ingeschakeld. Voeg * toe om alle commando's toe te staan (g<PERSON><PERSON><PERSON> met voorzichtigheid).", "deniedCommands": "Geweigerde commando's", "deniedCommandsDescription": "Commando-prefixen die automatisch worden geweigerd zonder om goedkeuring te vragen. <PERSON><PERSON><PERSON> <PERSON><PERSON> met toegestane commando's heeft de langste prefixovereenkomst voorrang. Voeg * toe om alle commando's te weigeren.", "commandPlaceholder": "Voer commando-prefix in (bijv. 'git ')", "deniedCommandPlaceholder": "Voer te weigeren commando-prefix in (bijv. 'rm -rf')", "addButton": "Toevoegen", "autoDenied": "Commando's met het prefix `{{prefix}}` zijn verboden door de gebruiker. Omzeil deze beperking niet door een ander commando uit te voeren."}, "updateTodoList": {"label": "Todo", "description": "De takenlijst wordt automatisch bijgewerkt zonder goedkeuring"}, "apiRequestLimit": {"title": "Maximale verzoeken", "description": "Voer automatisch dit aantal API-verzoeken uit voordat om goedkeuring wordt gevraagd om door te gaan met de taak.", "unlimited": "Onbeperkt"}, "selectOptionsFirst": "Selecteer ten minste één optie hieronder om automatische goedkeuring in te schakelen"}, "providers": {"providerDocumentation": "{{provider}} documentatie", "configProfile": "Configuratieprofiel", "description": "Sla verschillende API-configuraties op om snel te wisselen tussen providers en instellingen.", "apiProvider": "API-provider", "model": "Model", "nameEmpty": "<PERSON>am mag niet leeg zijn", "nameExists": "<PERSON><PERSON> <PERSON><PERSON><PERSON> al een profiel met deze naam", "nameTooLong": "<PERSON>am mag niet langer zijn dan 20 tekens", "deleteProfile": "<PERSON><PERSON>", "invalidArnFormat": "Ongeldig ARN-formaat. Controleer de bovenstaande voorbeelden.", "enterNewName": "<PERSON>oer een nieuwe naam in", "addProfile": "<PERSON><PERSON>", "renameProfile": "<PERSON><PERSON>", "newProfile": "<PERSON><PERSON><PERSON> configuratieprofiel", "enterProfileName": "<PERSON><PERSON><PERSON> pro<PERSON> in", "createProfile": "<PERSON><PERSON>", "cannotDeleteOnlyProfile": "Kan het enige profiel niet verwijderen", "searchPlaceholder": "<PERSON><PERSON>", "searchProviderPlaceholder": "Zoek providers", "noProviderMatchFound": "Geen providers gevonden", "noMatchFound": "<PERSON><PERSON>eenkomende profielen gevonden", "vscodeLmDescription": "De VS Code Language Model API stelt je in staat modellen te draaien die door andere VS Code-extensies worden geleverd (waaronder GitHub Copilot). De eenvoudigste manier om te beginnen is door de Copilot- en Copilot Chat-extensies te installeren vanuit de VS Code Marketplace.", "awsCustomArnUse": "Voer een geldige Amazon Bedrock ARN in voor het model dat je wilt gebruiken. Voorbeeldformaten:", "awsCustomArnDesc": "<PERSON>org ervoor dat de regio in de ARN overeenkomt met je geselecteerde AWS-regio hierboven.", "openRouterApiKey": "OpenRouter API-sleutel", "getOpenRouterApiKey": "OpenRouter API-sleutel ophalen", "apiKeyStorageNotice": "API-sleutels worden veilig opgeslagen in de geheime opslag van VSCode", "glamaApiKey": "Glama API-sleutel", "getGlamaApiKey": "Glama API-sleutel ophalen", "useCustomBaseUrl": "Aangepaste basis-URL gebruiken", "useReasoning": "Redenering inschakelen", "useHostHeader": "Aangepaste Host-header g<PERSON><PERSON><PERSON>n", "useLegacyFormat": "Verouderd OpenAI API-formaat gebruiken", "customHeaders": "Aangepaste headers", "headerName": "Headernaam", "headerValue": "Headerwaard<PERSON>", "noCustomHeaders": "<PERSON><PERSON> aangep<PERSON> headers gede<PERSON>ieerd. Klik op de + knop om er een toe te voegen.", "requestyApiKey": "Requesty API-sleutel", "refreshModels": {"label": "<PERSON><PERSON>", "hint": "Open de instellingen opnieuw om de nieuwste modellen te zien.", "loading": "Modellenlijst wordt vernieuwd...", "success": "Modellenlijst succesvol vernieuwd!", "error": "<PERSON>n model<PERSON>st niet vern<PERSON>. Probeer het opnieuw."}, "getRequestyApiKey": "Requesty API-sleutel ophalen", "openRouterTransformsText": "Comprimeer prompts en berichtreeksen tot de contextgrootte (<a>OpenRouter Transforms</a>)", "anthropicApiKey": "Anthropic API-sleutel", "getAnthropicApiKey": "Anthropic API-sleutel ophalen", "anthropicUseAuthToken": "Anthropic API-sleutel als Authorization-header doorgeven in plaats van X-Api-Key", "chutesApiKey": "Chutes API-sleutel", "getChutesApiKey": "Chutes API-sleutel ophalen", "deepSeekApiKey": "DeepSeek API-sleutel", "getDeepSeekApiKey": "DeepSeek API-sleutel ophalen", "moonshotApiKey": "Moonshot API-sleutel", "getMoonshotApiKey": "Moonshot API-sleutel ophalen", "moonshotBaseUrl": "Moonshot-ingangspunt", "geminiApiKey": "Gemini API-sleutel", "getGroqApiKey": "Groq API-sleutel ophalen", "groqApiKey": "Groq API-sleutel", "getGeminiApiKey": "Gemini API-sleutel ophalen", "getHuggingFaceApiKey": "Hugging Face API-sleutel ophalen", "huggingFaceApiKey": "Hugging Face API-sleutel", "huggingFaceModelId": "Model ID", "huggingFaceLoading": "Laden...", "huggingFaceModelsCount": "({{count}} modellen)", "huggingFaceSelectModel": "Selecteer een model...", "huggingFaceSearchModels": "Zoek modellen...", "huggingFaceNoModelsFound": "<PERSON><PERSON> modellen gevo<PERSON>", "huggingFaceProvider": "Provider", "huggingFaceProviderAuto": "Automatisch", "huggingFaceSelectProvider": "Selecteer een provider...", "huggingFaceSearchProviders": "Zoek providers...", "huggingFaceNoProvidersFound": "Geen providers gevonden", "apiKey": "API-sleutel", "openAiApiKey": "OpenAI API-sleutel", "openAiBaseUrl": "Basis-URL", "getOpenAiApiKey": "OpenAI API-sleutel ophalen", "mistralApiKey": "Mistral API-sleutel", "getMistralApiKey": "Mistral / Codestral API-sleutel ophalen", "codestralBaseUrl": "Codestral basis-URL (optioneel)", "codestralBaseUrlDesc": "Stel een alternatieve URL in voor het Codestral-model.", "xaiApiKey": "xAI API-sleutel", "getXaiApiKey": "xAI API-sleutel ophalen", "litellmApiKey": "LiteLLM API-sleutel", "litellmBaseUrl": "LiteLLM basis-URL", "awsCredentials": "AWS-inloggegevens", "awsProfile": "AWS-profiel", "awsApiKey": "Amazon Bedrock API-sleutel", "awsProfileName": "AWS-profielnaam", "awsAccessKey": "AWS-toegangssleutel", "awsSecretKey": "AWS-geheime sleutel", "awsSessionToken": "AWS-<PERSON><PERSON><PERSON><PERSON>", "awsRegion": "AWS-regio", "awsCrossRegion": "Gebruik cross-region inference", "awsBedrockVpc": {"useCustomVpcEndpoint": "Aangepast VPC-eindpunt gebruiken", "vpcEndpointUrlPlaceholder": "Voer VPC-eindpunt URL in (optioneel)", "examples": "Voorbeelden:"}, "enablePromptCaching": "Prompt caching inschakelen", "enablePromptCachingTitle": "<PERSON><PERSON><PERSON> prompt caching in om de prestaties te verbeteren en de kosten te verlagen voor ondersteunde modellen.", "cacheUsageNote": "Let op: als je geen cachegebruik ziet, probeer dan een ander model te selecteren en vervolgens weer je gewenste model.", "vscodeLmModel": "Taalmodel", "vscodeLmWarning": "Let op: dit is een zeer experimentele integratie en ondersteuning door providers kan variëren. Krijg je een foutmelding dat een model niet wordt ondersteund, dan ligt dat aan de provider.", "googleCloudSetup": {"title": "Om Google Cloud Vertex AI te gebruiken, moet je:", "step1": "1. <PERSON><PERSON> een Google Cloud-account aan, schakel de Vertex AI API in en activeer de gewenste Claude-modellen.", "step2": "2. Installeer de Google Cloud CLI en configureer standaardreferenties voor applicaties.", "step3": "3. Of maak een serviceaccount met referenties."}, "googleCloudCredentials": "Google Cloud-referenties", "googleCloudKeyFile": "Google Cloud-sleutelbestandspad", "googleCloudProjectId": "Google Cloud-project-ID", "googleCloudRegion": "Google Cloud-regio", "lmStudio": {"baseUrl": "Basis-URL (optioneel)", "modelId": "Model-ID", "speculativeDecoding": "Speculatieve decodering inschakelen", "draftModelId": "Draft Model-ID", "draftModelDesc": "Draft-model moet uit dezelfde modelfamilie komen voor correcte speculatieve decodering.", "selectDraftModel": "Selecteer draft-model", "noModelsFound": "Geen draft-modellen gevonden. Zorg dat LM Studio draait met Server <PERSON> ingeschakeld.", "description": "LM Studio laat je modellen lokaal op je computer draaien. <PERSON><PERSON> hun <a>quickstart-gids</a> voor instructies. Je moet ook de <b>lokale server</b>-functie van LM Studio starten om het met deze extensie te gebruiken. <span>Let op:</span> <PERSON><PERSON><PERSON> gebruikt complexe prompts en werkt het beste met <PERSON><PERSON><PERSON><PERSON>. Minder krachtige modellen werken mogelijk niet zoals verwacht."}, "ollama": {"baseUrl": "Basis-URL (optioneel)", "modelId": "Model-ID", "description": "Ollama laat je modellen lokaal op je computer draaien. <PERSON><PERSON> hun quickstart-gids voor instructies.", "warning": "Let op: <PERSON><PERSON><PERSON> gebru<PERSON>t complexe prompts en werkt het beste met <PERSON><PERSON><PERSON><PERSON>. Minder krachtige modellen werken mogelijk niet zoals verwacht."}, "unboundApiKey": "Unbound API-sleutel", "getUnboundApiKey": "Unbound API-sleutel ophalen", "unboundRefreshModelsSuccess": "Modellenlijst bijgewerkt! U kunt nu kiezen uit de nieuwste modellen.", "unboundInvalidApiKey": "Ongeldige API-sleutel. Controleer uw API-sleutel en probeer het opnieuw.", "humanRelay": {"description": "<PERSON>n <PERSON>-s<PERSON><PERSON><PERSON> ve<PERSON>, maar de gebruiker moet helpen met kopi<PERSON>ren en plakken naar de webchat-AI.", "instructions": "Tijdens gebruik verschijnt een dialoogvenster en wordt het huidige bericht automatisch naar het klembord gekopieerd. Je moet deze plakken in webversies van AI (zoals ChatGPT of Claude), vervolgens het antwoord van de AI terugkopiëren naar het dialoogvenster en op bevestigen klikken."}, "openRouter": {"providerRouting": {"title": "OpenRouter-providerroutering", "description": "OpenRouter stuurt verzoeken naar de best beschikbare providers voor je model. Standaard worden verzoeken gebalanceerd over de beste providers voor maximale uptime. Je kunt echter een specifieke provider kiezen voor dit model.", "learnMore": "Meer informatie over providerroutering"}}, "customModel": {"capabilities": "Stel de mogelijkheden en prijzen in voor je aangepaste OpenAI-compatibele model. <PERSON><PERSON> met het op<PERSON><PERSON> van de modelmogelijkheden, want deze kunnen de prestaties van Zhanlu beïnvlo<PERSON>n.", "maxTokens": {"label": "Maximaal aantal outputtokens", "description": "Maximaal aantal tokens dat het model in een antwoord kan genereren. (Geef -1 op om de server het maximum te laten bepalen.)"}, "contextWindow": {"label": "Context<PERSON><PERSON><PERSON><PERSON>", "description": "Totaal aantal tokens (input + output) dat het model kan verwerken."}, "imageSupport": {"label": "Ondersteuning voor afbeeldingen", "description": "Kan dit model afbeeldingen verwerken en begrijpen?"}, "computerUse": {"label": "Computergebruik", "description": "<PERSON><PERSON> dit model met een browser werken? (bijv. <PERSON> 3.7 Sonnet)."}, "promptCache": {"label": "Prompt caching", "description": "Kan dit model prompts cachen?"}, "pricing": {"input": {"label": "Invoerprijs", "description": "Kosten per miljoen tokens in de input/prompt. Dit beïnvlo<PERSON>t de kosten van het verzenden van context en instructies naar het model."}, "output": {"label": "Uitvoerprijs", "description": "Kosten per miljoen tokens in het antwoord van het model. <PERSON>t beïnvlo<PERSON>t de kosten van gegenereerde inhoud en voltooiingen."}, "cacheReads": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kosten per miljoen tokens voor het lezen uit de cache. Dit is de prijs die wordt gerekend wanneer een gecachte reactie wordt opgehaald."}, "cacheWrites": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kosten per miljoen tokens voor het schrijven naar de cache. Dit is de prijs die wordt gerekend wanneer een prompt voor het eerst wordt gecachet."}}, "resetDefaults": "Standaardwaarden herstellen"}, "rateLimitSeconds": {"label": "Snelheidslimiet", "description": "Minimale tijd tussen API-verzoeken."}, "consecutiveMistakeLimit": {"label": "Fout- & Herhalingslimiet", "description": "Aantal opeenvolgende fouten of herhaalde acties voordat het dialoogvenster 'Roo ondervindt problemen' wordt weergegeven", "unlimitedDescription": "Onbeperkt aantal nieuwe pogingen ingeschakeld (automatisch doorgaan). Het dialoogvenster zal nooit verschijnen.", "warning": "⚠️ Instellen op 0 staat onbeperkte nieuwe pogingen toe, wat aanzienlijk API-gebruik kan verbruiken"}, "reasoningEffort": {"label": "Model redeneervermogen", "high": "<PERSON><PERSON>", "medium": "Middel", "low": "Laag"}, "setReasoningLevel": "Redeneervermogen inschakelen", "claudeCode": {"pathLabel": "<PERSON> Pad", "description": "Optioneel pad naar uw Claude Code CLI. Standaard 'claude' als niet ingesteld.", "placeholder": "Standaard: claude", "maxTokensLabel": "<PERSON> Output Tokens", "maxTokensDescription": "Maximaal aantal output-tokens voor Claude Code-reacties. Standaard is 8000."}}, "browser": {"enable": {"label": "Browserhulpmiddel inschakelen", "description": "<PERSON><PERSON> ing<PERSON>d, kan <PERSON><PERSON> een browser gebruiken om te interageren met websites wanneer modellen computergebruik ondersteunen."}, "viewport": {"label": "Viewport-grootte", "description": "Selecteer de viewport-grootte voor browserinteracties. Dit beïnvloedt hoe websites worden weergegeven en gebruikt.", "options": {"largeDesktop": "Groot bureaublad (1280x800)", "smallDesktop": "Klein bureaublad (900x600)", "tablet": "Tablet (768x1024)", "mobile": "Mobiel (360x640)"}}, "screenshotQuality": {"label": "Screenshotkwaliteit", "description": "Pas de WebP-kwaliteit van browserscreenshots aan. Hogere waarden geven duidelijkere screenshots maar verhogen het tokengebruik."}, "remote": {"label": "Gebruik externe browserverbinding", "description": "Verbind met een Chrome-browser die draait met remote debugging ingeschakeld (--remote-debugging-port=9222).", "urlPlaceholder": "Aangepaste URL (bijv. http://localhost:9222)", "testButton": "Verbinding testen", "testingButton": "<PERSON><PERSON> met testen...", "instructions": "Voer het DevTools Protocol hostadres in of laat leeg om lokale Chrome-instanties automatisch te detecteren. De knop Verbinding testen probeert de aangepaste URL als opgegeven, of detecteert automatisch als het veld leeg is."}}, "checkpoints": {"enable": {"label": "Automatische checkpoints inschakelen", "description": "<PERSON>n ingeschakeld, ma<PERSON><PERSON>lu automatisch checkpoints tijdens het uitvoeren van taken, zodat je eenvoudig wijzigingen kunt bekijken of terugzetten."}}, "notifications": {"sound": {"label": "Geluidseffecten inschakelen", "description": "<PERSON>n inges<PERSON>, spe<PERSON>t Z<PERSON>lu geluidseffecten af voor meldingen en gebeurtenissen.", "volumeLabel": "Volume"}, "tts": {"label": "Tekst-na<PERSON>-spra<PERSON> inschakelen", "description": "<PERSON>n ing<PERSON>d, leest Z<PERSON><PERSON> zijn antwoorden hardop voor via tekst-naar-spraak.", "speedLabel": "<PERSON><PERSON><PERSON><PERSON>"}}, "contextManagement": {"description": "Bepaal welke informatie wordt opgenomen in het contextvenster van de AI, wat invloed heeft op tokengebruik en antwoordkwaliteit", "autoCondenseContextPercent": {"label": "Drempelwaarde om intelligente contextcompressie te activeren", "description": "<PERSON><PERSON> het contextvenster deze drempelwaarde bereikt, zal zhanlu het automatisch comprimeren."}, "condensingApiConfiguration": {"label": "API-configuratie voor contextcondensatie", "description": "Selecteer welke API-configuratie gebruikt moet worden voor contextcondensatie. Laat leeg om de huidige actieve configuratie te gebruiken.", "useCurrentConfig": "Standaard"}, "customCondensingPrompt": {"label": "Aangepaste contextcondensatieprompt", "description": "Aangepaste systeemprompt voor contextcondensatie. Laat leeg om de standaardprompt te gebruiken.", "placeholder": "<PERSON><PERSON>r hier je aangepaste condensatieprompt in...\n\nJe kunt dezelfde structuur gebruiken als de standaardprompt:\n- Vorig gesprek\n- <PERSON><PERSON><PERSON> werk\n- Belangrijke technische concepten\n- Relevante bestanden en code\n- Probleemoplossing\n- Openstaande taken en volgende stappen", "reset": "Herstellen naar standaard", "hint": "Leeg = gebruik standaardprompt"}, "autoCondenseContext": {"name": "Automatisch intelligente contextcompressie activeren", "description": "<PERSON><PERSON>, zal z<PERSON>lu automatisch de context comprimeren wanneer de drempel wordt bereikt. <PERSON><PERSON> uit<PERSON>, kun je nog steeds handmatig contextcompressie activeren."}, "openTabs": {"label": "Limiet geopende tabbladen in context", "description": "Maximaal aantal geopende VSCode-tabbladen dat in de context wordt opgenomen. Hogere waarden geven meer context maar verhogen het tokengebruik."}, "workspaceFiles": {"label": "Limiet werkruimtebestanden in context", "description": "Maximaal aantal bestanden dat wordt opgenomen in details van de huidige werkmap. Hogere waarden geven meer context maar verhogen het tokengebruik."}, "rooignore": {"label": ".<PERSON><PERSON><PERSON><PERSON><PERSON>-bestanden tonen in lijsten en zoekopdrachten", "description": "<PERSON>n inges<PERSON>keld, worden bestanden die overeenkomen met patron<PERSON> in .<PERSON><PERSON><PERSON><PERSON><PERSON> getoond in lijsten met een slotje. Indien uitgeschakeld, worden deze bestanden volledig verborgen in lijsten en zoekopdrachten."}, "maxReadFile": {"label": "Automatisch afkappen bij bestandslezen", "description": "Zhanlu leest dit aantal regels wanneer het model geen begin/eindwaarden opgeeft. Als dit aantal lager is dan het totaal, genereert Zhanlu een index van codelijnen. Speciale gevallen: -1 laat Zhanlu het hele bestand lezen (zonder indexering), 0 leest geen regels en geeft alleen een minimale index. Lagere waarden minimaliseren het initiële contextgebruik en maken precieze vervolg-leesopdrachten mogelijk. Expliciete begin/eind-aanvragen worden niet door deze instelling beperkt.", "lines": "regels", "always_full_read": "Altijd volledig bestand lezen"}, "maxConcurrentFileReads": {"label": "Limiet gelijktijdige bestandslezingen", "description": "Maximum aantal bestanden dat de 'read_file' tool tegelijkertijd kan verwerken. Hogere waarden kunnen het lezen van meerdere kleine bestanden versnellen maar verhogen het geheugengebruik."}, "diagnostics": {"includeMessages": {"label": "Automatisch diagnostiek opnemen in context", "description": "<PERSON><PERSON> inges<PERSON>, worden diagnostische berichten (fouten) van bewerkte bestanden automatisch opgenomen in de context. Je kunt altijd handmatig alle werkruimte-diagnostiek opnemen met @problems."}, "maxMessages": {"label": "Maximale diagnostische berichten", "description": "Maximaal aantal diagnostische berichten dat per bestand moet worden opgenomen. Deze limiet geldt voor zowel automatische opname (wanneer checkbox is ingeschakeld) als handmatige @problems vermeldingen. Hogere waarden bieden meer context maar verhogen het tokengebruik.", "resetTooltip": "<PERSON>set naar standaar<PERSON> (50)", "unlimited": "Onbeperkte diagnostische berichten", "unlimitedLabel": "Onbeperkt"}, "delayAfterWrite": {"label": "Vertraging na het schrijven om diagnostiek potentiële problemen te laten detecteren", "description": "Wachttijd na het schrijven van bestanden voordat u doorgaat, zodat diagnostische hulpmiddelen wijzigingen kunnen verwerken en problemen kunnen detecteren."}}, "condensingThreshold": {"label": "Compressie trigger drem<PERSON><PERSON><PERSON>e", "selectProfile": "Drempelwaarde voor profiel configureren", "defaultProfile": "<PERSON><PERSON> standaard (alle profielen)", "defaultDescription": "Wan<PERSON> de context dit percentage bereikt, wordt het automatisch gecomprimeerd voor alle profielen tenzij ze aangepaste instellingen hebben", "profileDescription": "Aangepaste drempelwaarde alleen voor dit profiel (overschrijft globale standaard)", "inheritDescription": "Dit profiel erft de globale standaard drempelwaarde ({{threshold}}%)", "usesGlobal": "(gebruikt globaal {{threshold}}%)"}}, "terminal": {"basic": {"label": "Terminalinstellingen: Basis", "description": "Basis <PERSON>ins<PERSON>lingen"}, "advanced": {"label": "Terminalinstellingen: Geavanceerd", "description": "De volgende opties vereisen mogelijk een herstart van de terminal om de instelling toe te passen."}, "outputLineLimit": {"label": "Terminaluitvoerlimiet", "description": "Maximaal aantal regels dat wordt opgenomen in de terminaluitvoer bij het uitvoeren van commando's. Overtollige regels worden uit het midden verwijderd om tokens te besparen. <0>Meer informatie</0>"}, "outputCharacterLimit": {"label": "Tekenlimiet terminal", "description": "Maximaal aantal tekens dat moet worden opgenomen in de terminaluitvoer bij het uitvoeren van commando's. Deze limiet heeft voorrang op de regellimiet om geheugenproblemen door extreem lange regels te voorkomen. Bij overschrijding wordt de uitvoer afgekapt. <0>Meer informatie</0>"}, "shellIntegrationTimeout": {"label": "Terminal shell-integratie timeout", "description": "Maximale wachttijd voor het initialiseren van shell-integratie voordat commando's worden uitgevoerd. Voor gebruikers met lange shell-opstarttijden moet deze waarde mogelijk worden verhoogd als je 'Shell Integration Unavailable'-fouten ziet in de terminal. <0>Meer informatie</0>"}, "shellIntegrationDisabled": {"label": "Terminal shell-integratie uitschakelen", "description": "<PERSON><PERSON><PERSON> dit in als terminalcommando's niet correct werken of als je 'Shell Integration Unavailable'-fouten ziet. Dit gebruikt een eenvoudigere methode om commando's uit te voeren en omzeilt enkele geavanceerde terminalfuncties. <0>Meer informatie</0>"}, "commandDelay": {"label": "Terminalcommando-vertraging", "description": "Vertraging in milliseconden na het uitvoeren van een commando. De standaardinstelling van 0 schakelt de vertraging volledig uit. Dit kan helpen om te zorgen dat de uitvoer volledig wordt vastgelegd in terminals met timingproblemen. In de meeste terminals wordt dit geïmplementeerd door `PROMPT_COMMAND='sleep N'` te zetten en in Powershell wordt `start-sleep` toegevoegd aan het einde van elk commando. <PERSON>orspron<PERSON>ijk was dit een workaround voor VSCode bug#237208 en is mogelijk niet meer nodig. <0>Meer informatie</0>"}, "compressProgressBar": {"label": "Voortgangsbalk-uitvoer comprimeren", "description": "Indien ingeschakeld, verwerkt Zhanlu terminaluitvoer met carriage returns (\r) om te simuleren hoe een echte terminal inhoud weergeeft. Dit verwijdert tussenliggende voortgangsbalken en behoudt alleen de eindstatus, waardoor er meer contextruimte overblijft."}, "powershellCounter": {"label": "PowerShell-teller workaround inschakelen", "description": "<PERSON><PERSON> ing<PERSON>, voe<PERSON> een teller toe aan PowerShell-commando's om correcte uitvoering te garanderen. Dit helpt bij PowerShell-terminals die problemen hebben met het vastleggen van uitvoer."}, "zshClearEolMark": {"label": "ZSH EOL-markering wissen", "description": "<PERSON><PERSON> inges<PERSON>, wist <PERSON><PERSON><PERSON> de ZSH end-of-line markering door PROMPT_EOL_MARK='' te zetten. Dit voorkomt problemen met de interpretatie van uitvoer die eindigt met speciale tekens zoals '%'."}, "zshOhMy": {"label": "Oh My Zsh-integratie inschakelen", "description": "Indien ingeschakeld, zet Zhanlu ITERM_SHELL_INTEGRATION_INSTALLED=Yes om Oh My Zsh shell-integratiefuncties te activeren. Het toepassen van deze instelling kan een herstart van de IDE vereisen."}, "zshP10k": {"label": "Powerlevel10k-integratie inschakelen", "description": "Indien ingeschakeld, zet Zhanlu POWERLEVEL9K_TERM_SHELL_INTEGRATION=true om Powerlevel10k shell-integratiefuncties te activeren."}, "zdotdir": {"label": "ZDOTDIR-afhandeling inschakelen", "description": "<PERSON><PERSON> ing<PERSON>, ma<PERSON><PERSON> een tijdelijke map aan voor ZDOTDIR om zsh shell-integratie correct af te handelen. Dit zorgt ervoor dat VSCode shell-integratie goed werkt met zsh en je zsh-configuratie behouden blijft."}, "inheritEnv": {"label": "Omgevingsvariabelen overnemen", "description": "<PERSON>n ingeschakeld, neemt de terminal omgevingsvariabelen over van het bovenliggende VSCode-proces, zoals shell-integratie-instellingen uit het gebruikersprofiel. Dit schakelt direct de VSCode-instelling `terminal.integrated.inheritEnv` om. <0>Meer informatie</0>"}}, "advancedSettings": {"title": "Geavanceerde instellingen"}, "advanced": {"diff": {"label": "Bewerken via diffs inschakelen", "description": "Indien ingeschakeld kan Z<PERSON>lu sneller bestanden bewerken en worden afgekorte volledige-bestandswijzigingen automatisch geweigerd. Werkt het beste met het nieuwste Claude 3.7 Sonnet-model.", "strategy": {"label": "Diff-strategie", "options": {"standard": "Standaard (<PERSON><PERSON>)", "multiBlock": "Experimenteel: Multi-block diff", "unified": "Experimenteel: Unified diff"}, "descriptions": {"standard": "Standaard diff-strategie past wijzigingen toe op één codeblok tegelijk.", "unified": "Unified diff-strategie gebruikt meerdere methoden om diffs toe te passen en kiest de beste aanpak.", "multiBlock": "Multi-block diff-strategie laat toe om meerdere codeblokken in één verzoek bij te werken."}}, "matchPrecision": {"label": "Matchnauwke<PERSON><PERSON><PERSON>", "description": "Deze schuifregelaar bepaalt hoe nauwkeurig codeblokken moeten overeenkomen bij het toepassen van diffs. Lagere waarden laten flexibelere matching toe maar verhogen het risico op verkeerde vervangingen. Gebruik waarden onder 100% met uiterste voorzichtigheid."}}, "todoList": {"label": "Takenlijst-tool inschakelen", "description": "<PERSON><PERSON>, kan <PERSON><PERSON><PERSON><PERSON><PERSON> maken en beheren om de voortgang van taken bij te houden. <PERSON>t help<PERSON> complexe taken te organiseren in beheersbare stappen."}}, "completion": {"description": "Configureer de code-aanvullingsinstellingen om je ontwikkelingservaring te verbeteren.", "configureButton": "Configureren"}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Experimentele unified diff-strategie gebruiken", "description": "<PERSON><PERSON><PERSON> de experimentele unified diff-strategie in. Deze strategie kan het aantal herhalingen door model fouten verminderen, maar kan onverwacht gedrag of onjuiste bewerkingen veroorzaken. Alleen inschakelen als je de risico's begrijpt en wijzigingen zorgvuldig wilt controleren."}, "SEARCH_AND_REPLACE": {"name": "Experimentele zoek-en-vervang-tool gebruiken", "description": "<PERSON><PERSON><PERSON> de experimentele zoek-en-vervang-tool in, wa<PERSON><PERSON> meerdere instanties van een zoekterm in <PERSON>én verzoek kan vervangen."}, "INSERT_BLOCK": {"name": "Experimentele inhoud-invoeg-tool gebruiken", "description": "<PERSON>hakel de experimentele inhoud-invoeg-tool in, wa<PERSON><PERSON> inhoud op specifieke regelnummers kan invoegen zonder een diff te maken."}, "POWER_STEERING": {"name": "Experimentele 'power steering'-modus gebruiken", "description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>t model vaker aan de details van de huidige modusdefinitie. Dit leidt tot sterkere naleving van roldefinities en aangepaste instructies, maar gebruikt meer tokens per bericht."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Experimentele multi-block diff-tool gebruiken", "description": "<PERSON>n ingeschakeld, g<PERSON><PERSON><PERSON><PERSON> de multi-block diff-tool. Hiermee wordt geprobeerd meerdere codeblokken in het bestand in één verzoek bij te werken."}, "CONCURRENT_FILE_READS": {"name": "Gelijktijdig lezen van bestanden inschakelen", "description": "<PERSON><PERSON> ing<PERSON>, kan zhanlu meerdere bestanden in <PERSON><PERSON> verzoek lezen. <PERSON><PERSON> uit<PERSON>, moet zhanlu bestanden één voor één lezen. Uitschakelen kan helpen bij het werken met minder capabele modellen of wanneer u meer controle over bestandstoegang wilt."}, "MARKETPLACE": {"name": "Marketplace inschakelen", "description": "<PERSON><PERSON> ingeschakeld kun je MCP's en aangepaste modi uit de Marketplace installeren."}, "MULTI_FILE_APPLY_DIFF": {"name": "Gelijktijdige bestandsbewerkingen inschakelen", "description": "<PERSON><PERSON> ing<PERSON>, kan zhanlu meerdere bestanden in één verzoek bewerken. <PERSON><PERSON> uit<PERSON>kel<PERSON>, moet zhanlu bestanden één voor één bewerken. Het uitschakelen hiervan kan helpen wanneer je werkt met minder capabe<PERSON> modellen of wanneer je meer controle wilt over bestandswijzigingen."}}, "promptCaching": {"label": "Prompt caching inschakelen", "description": "<PERSON><PERSON> ing<PERSON>, g<PERSON><PERSON><PERSON><PERSON>t model met prompt caching om kosten te verlagen."}, "temperature": {"useCustom": "Aangepaste temperatuur gebruiken", "description": "<PERSON><PERSON><PERSON><PERSON> <PERSON> will<PERSON><PERSON><PERSON> in de antwoorden van het model.", "rangeDescription": "Hogere waarden maken de output will<PERSON><PERSON><PERSON>, lagere waarden maken deze deterministischer."}, "modelInfo": {"supportsImages": "Ondersteunt afbeeldingen", "noImages": "Ondersteunt geen afbeeldingen", "supportsComputerUse": "Ondersteunt computergebruik", "noComputerUse": "Ondersteunt geen computergebruik", "supportsPromptCache": "Ondersteunt prompt caching", "noPromptCache": "Ondersteunt geen prompt caching", "maxOutput": "Maximale output", "inputPrice": "Invoerprijs", "outputPrice": "Uitvoerprijs", "cacheReadsPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheWritesPrice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enableStreaming": "Streaming inschakelen", "enableR1Format": "R1-modelparameters inschakelen", "enableR1FormatTips": "<PERSON><PERSON> ingeschakeld zijn bij gebruik van R1-modellen zoals QWQ om 400-fouten te voorkomen", "useAzure": "Azure gebruiken", "azureApiVersion": "Azure API-versie instellen", "gemini": {"freeRequests": "* Gratis tot {{count}} verzoeken per minuut. <PERSON><PERSON><PERSON> is de prijs a<PERSON><PERSON><PERSON><PERSON><PERSON> van de promptgrootte.", "pricingDetails": "Zie prijsdetails voor meer info.", "billingEstimate": "* Facturering is een schatting - de exacte kosten hangen af van de promptgrootte."}}, "modelPicker": {"automaticFetch": "De extensie haalt automatisch de nieuwste lijst met modellen op van <serviceLink>{{serviceName}}</serviceLink>. Weet je niet welk model je moet kiezen? <PERSON><PERSON><PERSON> werkt het beste met <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Je kunt ook zoeken op 'free' voor gratis opties die nu beschikbaar zijn.", "label": "Model", "searchPlaceholder": "<PERSON><PERSON>", "noMatchFound": "<PERSON><PERSON>een<PERSON>ten gevonden", "useCustomModel": "Aangepast gebruiken: {{modelId}}"}, "footer": {"feedback": "Als u vragen of feedback heeft, aarzel dan niet om <qqDocsLink>een probleem te melden</qqDocsLink>.", "telemetry": {"label": "Anonieme fout- en gebruiksrapportage toestaan", "description": "Help Zhanlu te verbeteren door anonieme gebruiksgegevens en foutmeldingen te verzenden. Er worden nooit code, prompts of persoonlijke gegevens verzonden. Zie ons privacybeleid voor meer informatie."}, "settings": {"import": "Importeren", "export": "Exporteren", "reset": "Resetten"}}, "thinkingBudget": {"maxTokens": "Max tokens", "maxThinkingTokens": "Max denk-tokens"}, "validation": {"apiKey": "Je moet een geldige API-sleutel opgeven.", "awsRegion": "Je moet een regio kiezen om Amazon Bedrock te gebruiken.", "googleCloud": "Je moet een geldig Google Cloud Project-ID en regio opgeven.", "modelId": "Je moet een geldig model-ID opgeven.", "modelSelector": "Je moet een geldige modelselector opgeven.", "openAi": "Je moet een geldige basis-URL, API-sleutel en model-ID opgeven.", "arn": {"invalidFormat": "Ongeldig ARN-formaat. Controleer de formaatvereisten.", "regionMismatch": "Waarschuwing: De regio in je ARN ({{arnRegion}}) komt niet overeen met je geselecteerde regio ({{region}}). Dit kan toegangsfouten veroorzaken. De provider gebruikt de regio uit de ARN."}, "modelAvailability": "Het opgegeven model-ID ({{modelId}}) is niet be<PERSON><PERSON>. <PERSON><PERSON> een ander model.", "providerNotAllowed": "Provider '{{provider}}' is niet toe<PERSON><PERSON>an door je organisatie", "modelNotAllowed": "Model '{{model}}' is niet <PERSON><PERSON><PERSON><PERSON> voor provider '{{provider}}' door je organisatie", "profileInvalid": "Dit profiel bevat een provider of model dat niet is toegestaan door je organisatie"}, "placeholders": {"apiKey": "Voer API-sleutel in...", "profileName": "<PERSON><PERSON><PERSON> pro<PERSON> in", "accessKey": "<PERSON><PERSON><PERSON> in...", "secretKey": "<PERSON><PERSON><PERSON> geheime sleutel in...", "sessionToken": "<PERSON><PERSON><PERSON> in...", "credentialsJson": "<PERSON><PERSON>r Credentials JSON in...", "keyFilePath": "<PERSON><PERSON><PERSON> pad naar sleutelbestand in...", "projectId": "Voer project-ID in...", "customArn": "Voer ARN in (bijv. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Voer basis-URL in...", "modelId": {"lmStudio": "bijv. meta-llama-3.1-8b-instruct", "lmStudioDraft": "bijv. lmstudio-community/llama-3.2-1b-instruct", "ollama": "bijv. llama3.1"}, "numbers": {"maxTokens": "bijv. 4096", "contextWindow": "bijv. 128000", "inputPrice": "bijv. 0.0001", "outputPrice": "bijv. 0.0002", "cacheWritePrice": "bijv. 0.00005"}}, "defaults": {"ollamaUrl": "Standaard: http://localhost:11434", "lmStudioUrl": "Standaard: http://localhost:1234", "geminiUrl": "Standaard: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "Aangepaste ARN", "useCustomArn": "Aangepaste ARN gebruiken..."}, "includeMaxOutputTokens": "Maximale output tokens opnemen", "includeMaxOutputTokensDescription": "Stuur maximale output tokens parameter in API-verzoeken. Sommige providers ondersteunen dit mogelijk niet.", "limitMaxTokensDescription": "Beperk het maximale aantal tokens in het antwoord", "maxOutputTokensLabel": "Maximale output tokens", "maxTokensGenerateDescription": "Maximale tokens om te genereren in het antwoord"}