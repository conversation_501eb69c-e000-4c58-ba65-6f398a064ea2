{"greeting": "Welkom <PERSON><PERSON>", "task": {"title": "<PERSON><PERSON>", "seeMore": "<PERSON><PERSON> weer<PERSON><PERSON>", "seeLess": "<PERSON><PERSON> weer<PERSON><PERSON>", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API-kosten:", "contextWindow": "Contextlengte:", "closeAndStart": "<PERSON><PERSON> sluiten en een nieuwe starten", "export": "Taakgeschiedenis exporteren", "delete": "<PERSON><PERSON> verwijderen (<PERSON><PERSON> + <PERSON><PERSON> om bevestiging over te slaan)", "condenseContext": "Context intelligent <PERSON><PERSON><PERSON><PERSON>", "share": "<PERSON><PERSON> delen", "shareWithOrganization": "<PERSON><PERSON> met organisa<PERSON>", "shareWithOrganizationDescription": "<PERSON><PERSON> leden van je organisatie kunnen toegang krijgen", "sharePublicly": "<PERSON><PERSON><PERSON>", "sharePubliclyDescription": "<PERSON><PERSON><PERSON> met de link kan toegang k<PERSON>", "connectToCloud": "<PERSON><PERSON><PERSON><PERSON> met <PERSON>", "connectToCloudDescription": "Meld je aan bij zhan<PERSON> om taken te delen", "sharingDisabledByOrganization": "Delen uitgeschakeld door organisatie", "shareSuccessOrganization": "Organisatielink gekopieerd naar klembord", "shareSuccessPublic": "Openbare link gekopieerd naar klembord"}, "unpin": "Losmaken", "pin": "Vastmaken", "retry": {"title": "Opnieuw proberen", "tooltip": "<PERSON><PERSON><PERSON> de bewerking opnieuw"}, "startNewTask": {"title": "<PERSON><PERSON><PERSON> taak starten", "tooltip": "Begin een nieuwe taak"}, "proceedAnyways": {"title": "<PERSON><PERSON> doorgaan", "tooltip": "Ga door terwijl het commando wordt uitgevoerd"}, "save": {"title": "Opsla<PERSON>", "tooltip": "Berichtwijzigingen opslaan"}, "tokenProgress": {"availableSpace": "Beschikbare ruimte: {{amount}} tokens", "tokensUsed": "Gebruikte tokens: {{used}} van {{total}}", "reservedForResponse": "Gereserveerd voor modelantwoord: {{amount}} tokens"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Deze actie weigeren"}, "completeSubtaskAndReturn": "Subtaak voltooien en terugkeren", "approve": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Deze actie goedkeuren"}, "runCommand": {"title": "Commando uitvoeren", "tooltip": "<PERSON><PERSON>r dit commando uit"}, "proceedWhileRunning": {"title": "Doorgaan tijdens uitvoeren", "tooltip": "Ga door ondanks waarschuwingen"}, "killCommand": {"title": "Commando stoppen", "tooltip": "Huidig commando stoppen"}, "resumeTask": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON>a door met de huidige taak"}, "terminate": {"title": "Beëindigen", "tooltip": "Beëindig de huidige taak"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> de huidige bewerking"}, "scrollToBottom": "<PERSON><PERSON> naar on<PERSON>aan de chat", "about": "<PERSON><PERSON>, corrigeer, refactor en debug code met behul<PERSON> van het Zhanlu AI-model.<br /><PERSON><PERSON><PERSON> onze <DocsLink>documentatie</DocsLink> voor meer informatie.", "onboarding": "De takenlijst in deze werkruimte is leeg. Begin door hieronder je taak in te voeren.<br>Weet je niet hoe je moet beginnen? <PERSON><PERSON> meer in onze <DocsLink>documentatie</DocsLink>.", "zhanluTips": {"boomerangTasks": {"title": "<PERSON><PERSON>", "description": "Splits taken op in kleinere, beheers<PERSON>e delen"}, "stickyModels": {"title": "Vastgezette modellen", "description": "Elke modus onthoudt je laatst gebruikte model"}, "tools": {"title": "Tools", "description": "Laat de AI problemen oplossen door te browsen, commando's uit te voeren en meer"}, "customizableModes": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> modi", "description": "Gespecialiseerde persona's met hun eigen gedrag en toegewezen modellen"}, "architect": {"title": "Architect <PERSON><PERSON>", "description": "Plan systeemarchitectuur en ontwerp codestructuren."}, "code": {"title": "Code Modus", "description": "<PERSON><PERSON>, wijzig en optimaliseer code."}, "unit_test": {"title": "Unittestmodus", "description": "Maak uitgebreide tests om codestabiliteit te waarborgen."}, "project_fix": {"title": "Projectreparatiemodus", "description": "Identificeer en repareer problemen of bugs in het project."}, "security_fix": {"title": "Beveiligingsreparatiemodus", "description": "Identificeer en los beveiligingskwetsbaarheden in code op."}, "code_review": {"title": "Codebeoordelingsmodus", "description": "Analyseer codekwaliteit en bied verbeteringsvoorstellen."}, "documentation": {"title": "Documentatiemodus", "description": "Maak duidelijke en gedetailleerde documentatie en instructies."}, "qa": {"title": "Vraag-en-antwoordmodus", "description": "Beantwoord vragen en bied eenvoudige ondersteuning."}}, "selectMode": "Selecteer modus voor interactie", "selectApiConfig": "Selecteer API-configuratie", "internetSearch": "Nadat u het zoeken op het internet hebt ingeschakeld, kunt u gerelateerde inhoud op het internet zoeken", "internetSearchClosed": "Internet zoeken sluiten", "enhancePrompt": "Prompt verbeteren met extra context", "enhancePromptDescription": "De knop 'Prompt verbeteren' helpt je prompt te verbeteren door extra context, verduid<PERSON><PERSON>ing of herformulering te bieden. <PERSON>beer hier een prompt te typen en klik opnieuw op de knop om te zien hoe het werkt.", "modeSelector": {"title": "<PERSON><PERSON>", "marketplace": "Modus <PERSON>", "settings": "Modus Instellingen", "description": "Gespecialiseerde persona's die het gedrag van z<PERSON>lu a<PERSON>passen."}, "addImages": "Afbeeldingen toevoegen aan bericht", "sendMessage": "Bericht verzenden", "stopTts": "Stop tekst-naar-spraak", "typeMessage": "Typ een bericht...", "typeTask": "<PERSON>p hier je taak...", "addContext": "@ Voeg context toe, / schakel modus, # snelopdrachten", "dragFiles": "<PERSON><PERSON> ingedrukt om bestanden te slepen", "dragFilesImages": "<PERSON><PERSON> Shift ingedrukt om bestanden/afbeeldingen te slepen", "errorReadingFile": "Fout bij het lezen van bestand:", "noValidImages": "Er zijn geen geldige afbeeldingen verwerkt", "separator": "Scheidingsteken", "edit": "Bewerken...", "forNextMode": "voor volgende modus", "forPreviousMode": "voor vorige modus", "apiRequest": {"title": "API-verzoek", "failed": "API-verz<PERSON>k mis<PERSON>t", "streaming": "API-verzoek...", "cancelled": "API-verz<PERSON>k g<PERSON>", "streamingFailed": "API-streaming mislukt"}, "checkpoint": {"initial": "Initiële checkpoint", "regular": "Checkpoint", "initializingWarning": "Checkpoint wordt nog steeds geïnitialiseerd... Als dit te lang duurt, kun je checkpoints uitschakelen in de <settingsLink>instellingen</settingsLink> en je taak opnieuw starten.", "menu": {"viewDiff": "Bekijk verschil", "restore": "Herstel checkpoint", "restoreFiles": "<PERSON><PERSON><PERSON>", "restoreFilesDescription": "Herstelt de bestanden van je project naar een momentopname die op dit punt is gemaakt.", "restoreFilesAndTask": "Bestanden & taak herstellen", "confirm": "Bevestigen", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Deze actie kan niet ongedaan worden gemaakt.", "restoreFilesAndTaskDescription": "Herstelt de bestanden van je project naar een momentopname die op dit punt is gemaakt en verwijdert alle berichten na dit punt."}, "current": "<PERSON><PERSON><PERSON>"}, "instructions": {"wantsToFetch": "<PERSON><PERSON><PERSON> wil g<PERSON><PERSON><PERSON><PERSON> instructies op<PERSON>n om te helpen met de huidige taak"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON><PERSON> wil dit bestand lezen:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON><PERSON> wil dit bestand buiten de werkruimte lezen:", "didRead": "<PERSON><PERSON><PERSON> heeft dit bestand gelezen:", "wantsToEdit": "<PERSON><PERSON><PERSON> wil dit bestand bewerken:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON><PERSON> wil dit bestand buiten de werkruimte bewerken:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> wil een beveiligd configuratiebestand bewerken:", "wantsToCreate": "<PERSON><PERSON><PERSON> wil een nieuw bestand aan<PERSON>ken:", "wantsToSearchReplace": "<PERSON><PERSON><PERSON> wil zoeken en vervangen in dit bestand:", "didSearchReplace": "<PERSON><PERSON><PERSON> heeft zoeken en vervangen uitgevoerd op dit bestand:", "wantsToInsert": "<PERSON><PERSON><PERSON> wil inhoud invoegen in dit bestand:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> wil inhoud invoegen in dit bestand op regel {{lineNumber}}:", "wantsToInsertAtEnd": "<PERSON><PERSON><PERSON> wil inhoud toevoegen aan het einde van dit bestand:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> wil dit bestand en nog {{count}} andere lezen:", "wantsToReadMultiple": "<PERSON><PERSON><PERSON> wil meerdere bestanden lezen:", "wantsToApplyBatchChanges": "<PERSON><PERSON><PERSON> wil wijzigingen toepassen op meerdere bestanden:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON><PERSON> wil de bovenliggende bestanden in deze map bekijken:", "didViewTopLevel": "<PERSON><PERSON><PERSON> heeft de bovenliggende bestanden in deze map bekeken:", "wantsToViewRecursive": "<PERSON><PERSON><PERSON> wil alle bestanden in deze map recursief bekijken:", "didViewRecursive": "<PERSON><PERSON><PERSON> heeft alle bestanden in deze map recursief bekeken:", "wantsToViewDefinitions": "<PERSON><PERSON><PERSON> wil broncode-definitienamen bekijken die in deze map worden gebruikt:", "didViewDefinitions": "Zhanlu heeft broncode-definitienamen bekeken die in deze map worden gebruikt:", "wantsToSearch": "<PERSON><PERSON><PERSON> wil deze map doorzoeken op <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON><PERSON> heeft deze map doorzocht op <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> wil deze map (buiten werkruimte) doorzoeken op <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> heeft deze map (buiten werkruimte) doorzocht op <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> wil de bovenliggende bestanden in deze map (buiten werkruimte) bekijken:", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> heeft de bovenliggende bestanden in deze map (buiten werkruimte) bekeken:", "wantsToViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> wil alle bestanden in deze map (buiten werkruimte) recursief bekijken:", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> heeft alle bestanden in deze map (buiten werkruimte) recursief bekeken:", "wantsToViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON> wil broncode-definitienamen bekijken die in deze map (buiten werkruimte) worden gebruikt:", "didViewDefinitionsOutsideWorkspace": "Zhanlu heeft broncode-definitienamen bekeken die in deze map (buiten werkruimte) worden gebruikt:"}, "commandOutput": "Commando-uitvoer", "commandExecution": {"running": "Lopend", "pid": "PID: {{pid}}", "exited": "Afgesloten ({{exitCode}})", "manageCommands": "Beheer Commando Toestemmingen", "commandManagementDescription": "Beheer commando toestemmingen: Klik op ✓ om automatische uitvoering toe te staan, ✗ om uitvoering te weigeren. Patronen kunnen worden in- of uitgeschakeld of uit lijsten worden verwijderd. <settingsLink>Bekijk alle instellingen</settingsLink>", "addToAllowed": "Toevoegen aan toe<PERSON>e lijst", "removeFromAllowed": "Verwijderen van toegestane lijst", "addToDenied": "Toevoegen aan geweigerde lijst", "removeFromDenied": "Verwijderen van geweigerde lijst", "abortCommand": "Commando-uitvoering afbreken", "expandOutput": "Uitvoer uitvouwen", "collapseOutput": "Uitvoer <PERSON>", "expandManagement": "Beheersectie voor commando's uitvouwen", "collapseManagement": "Beheersectie voor commando's same<PERSON><PERSON><PERSON><PERSON>"}, "response": "Antwoord", "arguments": "Argumenten", "mcp": {"wantsToUseTool": "<PERSON><PERSON><PERSON> wil een tool gebruiken op de {{serverName}} MCP-server:", "wantsToAccessResource": "<PERSON><PERSON><PERSON> wil een bron benaderen op de {{serverName}} MCP-server:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> wil overschakelen naar {{mode}} modus", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> wil overschakelen naar {{mode}} modus omdat: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> is overgeschakeld naar {{mode}} modus", "didSwitchWithReason": "<PERSON><PERSON><PERSON> is overgeschakeld naar {{mode}} modus omdat: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> wil een nieuwe subtaak aanmaken in {{mode}} modus:", "wantsToFinish": "<PERSON><PERSON><PERSON> wil deze subtaak voltooien", "newTaskContent": "Subtaak-instructies", "completionContent": "Subtaak voltooid", "resultContent": "Subtaakresultaten", "defaultResult": "<PERSON><PERSON> <PERSON><PERSON><PERSON> met de volgende taak.", "completionInstructions": "Subtaak voltooid! Je kunt de resultaten bekijken en eventuele correcties of volgende stappen voorstellen. Als alles goed is, bevestig dan om het resultaat terug te sturen naar de hoofdtaak."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> heeft een vraag:"}, "taskCompleted": "Taak voltooid", "error": "Fout", "warning": "Waarschuwing", "diffError": {"title": "Bewerking mislukt"}, "troubleMessage": "<PERSON><PERSON><PERSON> problemen...", "powershell": {"issues": "Het lijkt erop dat je problemen hebt met Windows PowerShell, zie deze"}, "autoApprove": {"title": "Automatisch goedkeuren:", "none": "<PERSON><PERSON>", "description": "Met automatisch goedkeuren kan zhanlu acties uitvoeren zonder om toestemming te vragen. <PERSON><PERSON><PERSON> dit alleen in voor acties die je volledig vertrouwt. Meer gedetailleerde configuratie besch<PERSON> in de <settingsLink>Instellingen</settingsLink>.", "selectOptionsFirst": "Selecteer hieron<PERSON> minstens één optie om automatische goedkeuring in te schakelen", "toggleAriaLabel": "Automatisch goedkeuren in-/uitschakelen", "disabledAriaLabel": "Automatisch goedkeuren uitgeschakeld - selecteer eerst opties"}, "announcement": {"title": "🎉 <PERSON><PERSON><PERSON> Versie 2.3.2 Update", "description": "Bugfixes, code-aanvulling", "whatsNew": "Belangrijke updates", "feature1": "<bold>Code Foundation Modus toegevoegd</bold>: Code Foundation Modus genereert programmeeroefeningen", "feature2": "<bold>Geschiedenis taak bugfix</bold>: Probleem opgelost waarbij nieuwe taken verschijnen in geschiedenis taken", "feature3": "<bold>Flicker probleem fix</bold>: <PERSON><PERSON> schermflikker probleem bij het weergeven van code opgelost", "feature4": "<bold>Andere optimalisaties</bold>: Optimalisaties van verschillende andere problemen", "hideButton": "Aankondiging verbergen", "detailsDiscussLinks": "Bekijk <discordLink>gedetailleerde documentatie</discordLink> om meer functies te leren kennen ��"}, "reasoning": {"thinking": "Denkt na", "seconds": "{{count}}s"}, "contextCondense": {"title": "Context same<PERSON>", "condensing": "Context aan het samenvatten...", "errorHeader": "Context same<PERSON><PERSON>ten mislukt", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "<PERSON><PERSON><PERSON><PERSON> naar invoer (zelfde als shift + klik)", "autoSelectCountdown": "Automatische selectie in {{count}}s", "countdownDisplay": "{{count}}s"}, "browser": {"rooWantsToUse": "<PERSON><PERSON><PERSON> wil de <PERSON> gebruiken:", "consoleLogs": "Console-logboeken", "noNewLogs": "(Geen nieuwe logboeken)", "screenshot": "Browserschermopname", "cursor": "cursor", "navigation": {"step": "Stap {{current}} van {{total}}", "previous": "Vorige", "next": "Volgende"}, "sessionStarted": "<PERSON>rows<PERSON>ssie gestart", "actions": {"title": "Browse-actie: ", "launch": "Browser starten op {{url}}", "click": "<PERSON>lik ({{coordinate}})", "type": "Typ \"{{text}}\"", "scrollDown": "<PERSON><PERSON> naar beneden", "scrollUp": "<PERSON><PERSON> naar boven", "close": "Browser sluiten"}}, "codeblock": {"tooltips": {"expand": "Codeblok uitvouwen", "collapse": "Codeblok <PERSON>wen", "enable_wrap": "Regelafbreking inschakelen", "disable_wrap": "Regelafbreking uitschakelen", "copy_code": "Code kopiëren"}}, "qucikInstructions": {"UiToCode": "UI ontwerp diagram genereren van code", "UmlToCode": "UML Diagram Generatie Code", "ExplainCode": "Code uitleg", "FixCode": "Code fouten corrigeren", "ImproveCode": "Code optimalisatie", "UnitTest": "Eenheidstest", "CODE_REVIEW": "Code beoordeling", "CommentCode": "Code commentaar", "PlusButtonClicked": "Het dialoogvenster leeg maken"}, "systemPromptWarning": "WAARSCHUWING: Aangepaste systeemprompt actief. Dit kan de functionaliteit ernstig verstoren en onvoorspelbaar gedrag veroorzaken.", "profileViolationWarning": "Het huidige profiel is niet compatibel met de instellingen van uw organisatie", "shellIntegration": {"title": "Waarschuwing commando-uitvoering", "description": "Je commando wordt uitgevoerd zonder VSCode-terminal shell-integratie. Om deze waarschuwing te onderdrukken kun je shell-integratie uitschakelen in het gedeelte <strong>Terminal</strong> van de <settingsLink>Zhanlu-instellingen</settingsLink> of de VSCode-terminalintegratie oplossen via de onderstaande link.", "troubleshooting": "Klik hier voor shell-integratie documentatie."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limiet voor automatisch goedgekeurde verzoeken bereikt", "description": "<PERSON><PERSON><PERSON> heeft de automatisch goedgekeurde limiet van {{count}} API-verzoek(en) bereikt. Wil je de teller resetten en doorgaan met de taak?", "button": "Resetten en doorgaan"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON><PERSON> wil de codebase doorzoeken op <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> wil de codebase doorzoeken op <code>{{query}}</code> in <code>{{path}}</code>:", "didSearch_one": "1 resultaat gevonden", "didSearch_other": "{{count}} resultaten gevonden", "resultTooltip": "Gelijkenisscore: {{score}} (klik om bestand te openen)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON> go<PERSON>"}, "deny": {"title": "Alles weigeren"}}, "indexingStatus": {"ready": "Index gereed", "indexing": "Indexeren {{percentage}}%", "indexed": "Geïndexeerd", "error": "Index fout", "status": "Index status"}, "versionIndicator": {"ariaLabel": "Versie {{version}} - Klik om release notes te bekijken"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> <PERSON> komt binnenkort!", "description": "<PERSON>oer externe agenten uit in de cloud, krijg overal toegang tot je taken, werk samen met anderen en nog veel meer.", "joinWaitlist": "Sluit je aan bij de wachtlijst voor vroege toegang."}, "editMessage": {"placeholder": "Bewerk je bericht..."}}