{"title": "模式", "done": "完成", "modes": {"title": "模式", "createNewMode": "建立新模式", "importMode": "匯入模式", "noMatchFound": "找不到任何模式", "editModesConfig": "編輯模式設定", "editGlobalModes": "編輯全域模式", "editProjectModes": "編輯專案模式 (.<PERSON><PERSON><PERSON>)", "createModeHelpText": "點選 + 建立新的自訂模式，或者在聊天中直接請 Zhanlu 為您建立！", "selectMode": "搜尋模式"}, "apiConfiguration": {"title": "API 設定", "select": "選擇要用於此模式的 API 設定"}, "tools": {"title": "可用工具", "builtInModesText": "內建模式的工具無法修改", "editTools": "編輯工具", "doneEditing": "完成編輯", "allowedFiles": "允許的檔案：", "toolNames": {"read": "讀取檔案", "edit": "編輯檔案", "browser": "使用瀏覽器", "command": "執行命令", "mcp": "使用 MCP"}, "noTools": "無"}, "roleDefinition": {"title": "角色定義", "resetToDefault": "重設為預設值", "description": "定義此模式下 Zhanlu 的專業知識和個性。此描述會形塑 Zhanlu 如何展現自己並處理工作。"}, "description": {"title": "簡短描述（給人看的）", "resetToDefault": "重置為預設描述", "description": "在模式選擇下拉選單中顯示的簡短描述。"}, "whenToUse": {"title": "使用時機（選用）", "description": "描述何時應使用此模式。這有助於 Orchestrator 為任務選擇適當的模式。", "resetToDefault": "重設「使用時機」描述為預設值"}, "customInstructions": {"title": "模式專屬自訂指令（選用）", "resetToDefault": "重設為預設值", "description": "為 {{modeName}} 模式新增專屬的行為指南。", "loadFromFile": "{{mode}} 模式的自訂指令也可以從工作區的 <span>.zhanlu/rules-{{slug}}/</span> 資料夾載入。"}, "exportMode": {"title": "匯出模式", "description": "將此模式匯出為包含所有規則的 YAML 檔案，以便與他人輕鬆分享。", "export": "匯出模式", "exporting": "正在匯出..."}, "importMode": {"selectLevel": "選擇匯入模式的位置：", "import": "匯入", "importing": "匯入中...", "global": {"label": "全域", "description": "適用於所有專案。規則將合併到自訂指令中。"}, "project": {"label": "專案級", "description": "僅在此工作區可用。如果匯出的模式包含規則檔案，則將在 .zhanlu/rules-{slug}/ 資料夾中重新建立這些檔案。"}}, "globalCustomInstructions": {"title": "所有模式的自訂指令", "description": "這些指令適用於所有模式。它們提供了一組基本行為，可以透過下方的模式專屬自訂指令來強化。\n如果您希望 Zhanlu 使用與編輯器顯示語言 ({{language}}) 不同的語言來思考和對話，您可以在這裡指定。", "loadFromFile": "指令也可以從工作區的 <span>.zhanlu/rules/</span> 資料夾載入。"}, "systemPrompt": {"preview": "預覽系統提示詞", "copy": "複製系統提示詞到剪貼簿", "title": "系統提示詞（{{modeName}} 模式）"}, "supportPrompts": {"title": "輔助提示詞", "resetPrompt": "將 {{promptType}} 提示詞重設為預設值", "prompt": "提示詞", "enhance": {"apiConfiguration": "API 設定", "apiConfigDescription": "您可以選擇一個固定的 API 設定用於增強提示詞，或使用目前選擇的設定", "useCurrentConfig": "使用目前選擇的 API 設定", "testPromptPlaceholder": "輸入提示詞以測試增強效果", "previewButton": "預覽提示詞增強", "testEnhancement": "測試增強"}, "condense": {"apiConfiguration": "用於上下文壓縮的 API 設定", "apiConfigDescription": "選取用於上下文壓縮作業的 API 設定。保留未選取狀態以使用目前作用中的設定。", "useCurrentConfig": "使用當前選取的 API 設定"}, "types": {"ENHANCE": {"label": "增強提示詞", "description": "使用提示詞增強功能取得針對您輸入的客製化建議或改進。這確保 Zhanlu 能理解您的意圖並提供最佳的回應。可透過聊天中的 ✨ 圖示使用。"}, "CONDENSE": {"label": "上下文壓縮", "description": "設定如何壓縮對話上下文以管理權杖限制。此提示用於手動和自動上下文壓縮作業。"}, "EXPLAIN": {"label": "解釋程式碼", "description": "取得程式碼片段、函式或整個檔案的詳細解釋。有助於理解複雜程式碼或學習新模式。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "FIX": {"label": "修復問題", "description": "協助識別和解決錯誤、程式臭蟲或程式碼品質問題。提供逐步修復問題的指引。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "IMPROVE": {"label": "改進程式碼", "description": "在維持功能的同時，提供程式碼最佳化、最佳實踐和架構改進的建議。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "ADD_TO_CONTEXT": {"label": "新增至上下文", "description": "為目前工作或對話新增上下文。用於提供額外資訊或說明。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "TERMINAL_ADD_TO_CONTEXT": {"label": "新增終端機內容至上下文", "description": "將終端機輸出新增至目前工作或對話。用於提供命令輸出或記錄。可在終端機右鍵選單中使用。"}, "TERMINAL_FIX": {"label": "修復終端機命令", "description": "協助修復失敗或需要改進的終端機命令。可在終端機右鍵選單中使用。"}, "TERMINAL_EXPLAIN": {"label": "說明終端機命令", "description": "取得終端機命令及其輸出的詳細說明。可在終端機內容選單（選取終端機內容後按右鍵）使用。"}, "NEW_TASK": {"label": "開始新工作", "description": "開始一個新的工作。可在命令選擇區使用。"}, "UNIT_TEST": {"label": "單元測試", "description": "為選取的程式碼產生完整的單元測試。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "CODE_REVIEW": {"label": "程式碼審查", "description": "對選取的程式碼執行詳細審查並提供改進建議。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}, "COMMENT_CODE": {"label": "程式碼註解", "description": "為選取的程式碼新增詳細註解和說明文件。可在程式碼操作（編輯器中的燈泡圖示）和編輯器右鍵選單中使用。"}}}, "advancedSystemPrompt": {"title": "進階：覆寫系統提示詞", "description": "您可以透過在工作區建立檔案 <span>.zhanlu/system-prompt-{{slug}}</span> 來完全替換此模式的系統提示詞（角色定義和自訂指令除外）。這是一個非常進階的功能，會繞過內建的安全措施和一致性檢查（尤其是與工具使用相關的檢查），請謹慎使用！"}, "createModeDialog": {"title": "建立新模式", "close": "關閉", "name": {"label": "名稱", "placeholder": "輸入模式名稱", "tooLong": "名稱不能超過 20 個字元"}, "slug": {"label": "識別符號", "description": "識別符號用於 URL 和檔案名稱。應使用小寫字母，且只能包含字母、數字和連字號。", "tooLong": "識別符號不能超過 20 個字元"}, "saveLocation": {"label": "儲存位置", "description": "選擇儲存此模式的位置。專案特定模式優先於全域模式。", "global": {"label": "全域", "description": "在所有工作區可用"}, "project": {"label": "專案特定 (.z<PERSON><PERSON>)", "description": "僅在此工作區可用，優先於全域模式"}}, "roleDefinition": {"label": "角色定義", "description": "定義此模式下 Zhanlu 的專業知識和個性。"}, "whenToUse": {"label": "使用時機（選用）", "description": "提供清晰的描述，說明此模式最適合什麼時候使用，以及適合哪些類型的任務。"}, "tools": {"label": "可用工具", "description": "選擇此模式可使用的工具。"}, "description": {"label": "簡短描述（給人看的）", "description": "在模式選擇下拉選單中顯示的簡短描述。"}, "customInstructions": {"label": "自訂指令（選用）", "description": "為此模式新增特定的行為指南。"}, "buttons": {"cancel": "取消", "create": "建立模式"}, "deleteMode": "刪除模式"}, "allFiles": "所有檔案", "advanced": {"title": "進階"}, "deleteMode": {"title": "刪除模式", "message": "您確定要刪除「{{modeName}}」模式嗎？", "rulesFolder": "此模式在 {{folderPath}} 有一個規則資料夾，該資料夾也將被刪除。", "descriptionNoRules": "您確定要刪除此自訂模式嗎？", "confirm": "刪除", "cancel": "取消"}}