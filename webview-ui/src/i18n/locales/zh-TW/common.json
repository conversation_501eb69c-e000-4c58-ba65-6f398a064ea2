{"errorBoundary": {"title": "發生錯誤", "reportText": "請幫助我們改進，在以下位置回報此錯誤", "githubText": "我們的 GitHub Issues 頁面", "copyInstructions": "複製並貼上以下錯誤訊息，將其作為提交內容的一部分：", "errorStack": "錯誤堆疊：", "componentStack": "元件堆疊："}, "answers": {"yes": "是", "no": "否", "cancel": "取消", "remove": "移除", "keep": "保留"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "搜尋..."}, "mermaid": {"loading": "產生 Mermaid 圖表中...", "render_error": "無法渲染圖表", "file_media": "流程預覽", "code": "源碼展示", "buttons": {"zoom": "縮放", "zoomIn": "放大", "zoomOut": "縮小", "copy": "複製", "save": "儲存圖片", "viewCode": "檢視程式碼", "viewDiagram": "檢視圖表", "close": "關閉"}, "modal": {"codeTitle": "Mermaid 程式碼"}, "tabs": {"diagram": "圖表", "code": "程式碼"}, "feedback": {"imageCopied": "圖片已複製到剪貼簿", "copyError": "複製圖片時發生錯誤"}}, "file": {"errors": {"invalidDataUri": "無效的資料 URI 格式", "copyingImage": "複製圖片時發生錯誤: {{error}}", "openingImage": "開啟圖片時發生錯誤: {{error}}", "pathNotExists": "路徑不存在: {{path}}", "couldNotOpen": "無法開啟檔案: {{error}}", "couldNotOpenGeneric": "無法開啟檔案！"}, "success": {"imageDataUriCopied": "圖片資料 URI 已複製到剪貼簿"}}, "confirmation": {"deleteMessage": "刪除訊息", "deleteWarning": "刪除此訊息將刪除對話中的所有後續訊息。是否繼續？", "editMessage": "編輯訊息", "editWarning": "編輯此訊息將刪除對話中的所有後續訊息。是否繼續？", "proceed": "繼續"}}