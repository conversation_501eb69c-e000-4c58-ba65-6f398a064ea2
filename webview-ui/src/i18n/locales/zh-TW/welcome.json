{"greeting": "歡迎使用 Roo Code！", "introduction": "透過一系列內建和可擴充的模式，Roo Code 讓您能夠以前所未有的方式進行規劃、架構設計、編碼、除錯並提升工作效率。", "notice": "開始使用前，此擴充功能需要一個 API 提供者。", "start": "讓我們開始吧！", "routers": {"requesty": {"description": "您的最佳化 LLM 路由器", "incentive": "$1 免費額度"}, "openrouter": {"description": "LLM 的統一介面"}}, "chooseProvider": "Roo 需要一個 API 金鑰才能發揮魔力。", "startRouter": "我們建議使用 LLM 路由器：", "startCustom": "或者您可以使用自己的 API 金鑰：", "telemetry": {"title": "協助改進 <PERSON>lu", "anonymousTelemetry": "傳送匿名的錯誤和使用資料，以協助我們修復錯誤並改進擴充功能。我們絕不會傳送任何程式碼、提示或個人資訊。", "changeSettings": "您隨時可以在<settingsLink>設定</settingsLink>底端更改此選項", "settings": "設定", "allow": "允許", "deny": "拒絕"}, "importSettings": "匯入設定"}