{"greeting": "<PERSON><PERSON><PERSON> में आपका स्वागत है", "task": {"title": "कार्य", "seeMore": "अधिक देखें", "seeLess": "कम देखें", "tokens": "Tokens:", "cache": "कैश:", "apiCost": "API लागत:", "contextWindow": "संदर्भ लंबाई:", "closeAndStart": "कार्य बंद करें और नया शुरू करें", "export": "कार्य इतिहास निर्यात करें", "delete": "कार्य हटाएं (पुष्टि को छोड़ने के लिए Shift + क्लिक)", "condenseContext": "संदर्भ को बुद्धिमानी से संघनित करें", "share": "कार्य साझा करें", "copyId": "कार्य आईडी कॉपी करें", "shareWithOrganization": "संगठन के साथ साझा करें", "shareWithOrganizationDescription": "केवल आपके संगठन के सदस्य पहुंच सकते हैं", "sharePublicly": "सार्वजनिक रूप से साझा करें", "sharePubliclyDescription": "लिंक वाला कोई भी व्यक्ति पहुंच सकता है", "connectToCloud": "Cloud से कनेक्ट करें", "connectToCloudDescription": "कार्य साझा करने के लिए zhan<PERSON> Cloud में साइन इन करें", "sharingDisabledByOrganization": "संगठन द्वारा साझाकरण अक्षम किया गया", "shareSuccessOrganization": "संगठन लिंक क्लिपबोर्ड में कॉपी किया गया", "shareSuccessPublic": "सार्वजनिक लिंक क्लिपबोर्ड में कॉपी किया गया"}, "unpin": "पिन करें", "pin": "अवपिन करें", "tokenProgress": {"availableSpace": "उपलब्ध स्थान: {{amount}} tokens", "tokensUsed": "प्रयुक्त tokens: {{used}} / {{total}}", "reservedForResponse": "मॉडल प्रतिक्रिया के लिए आरक्षित: {{amount}} tokens"}, "retry": {"title": "पुनः प्रयास करें", "tooltip": "ऑपरेशन फिर से प्रयास करें"}, "startNewTask": {"title": "नया कार्य शुरू करें", "tooltip": "नया कार्य शुरू करें"}, "proceedAnyways": {"title": "फिर भी आगे बढ़ें", "tooltip": "कमांड निष्पादन के दौरान जारी रखें"}, "save": {"title": "सहेजें", "tooltip": "संदेश के बदलाव सहेजें"}, "reject": {"title": "अस्वीकार करें", "tooltip": "इस क्रिया को अस्वीकार करें"}, "completeSubtaskAndReturn": "उपकार्य पूरा करें और वापस लौटें", "approve": {"title": "स्वीकृत करें", "tooltip": "इस क्रिया को स्वीकृत करें"}, "runCommand": {"title": "कमांड चलाएँ", "tooltip": "इस कमांड को निष्पादित करें"}, "proceedWhileRunning": {"title": "चलते समय आगे बढ़ें", "tooltip": "चेतावनियों के बावजूद जारी रखें"}, "killCommand": {"title": "कमांड रोकें", "tooltip": "वर्तमान कमांड रोकें"}, "resumeTask": {"title": "कार्य जारी रखें", "tooltip": "वर्तमान कार्य जारी रखें"}, "terminate": {"title": "समाप्त करें", "tooltip": "वर्तमान कार्य समाप्त करें"}, "cancel": {"title": "रद्<PERSON> करें", "tooltip": "वर्तमान ऑपरेशन रद्द करें"}, "scrollToBottom": "चैट के निचले हिस्से तक स्क्रॉल करें", "about": "<PERSON><PERSON><PERSON> AI मॉडल की मदद से कोड जनरेट करें, सुधारें, रिफैक्टर करें और डिबग करें।<br />अधिक जानकारी के लिए हमारा <DocsLink>दस्तावेज़ीकरण</DocsLink> देखें।", "onboarding": "इस कार्यक्षेत्र में कार्य सूची खाली है। नीचे अपना कार्य दर्ज करके शुरू करें।<br>शुरू कैसे करें, यह नहीं पता? हमारे <DocsLink>दस्तावेज़ीकरण</DocsLink> में अधिक पढ़ें।", "zhanluTips": {"boomerangTasks": {"title": "कार्य संयोजन", "description": "कार्यों को छोटे, प्रबंधनीय भागों में विभाजित करें।"}, "stickyModels": {"title": "स्टिकी मोड", "description": "प्रत्येक मोड आपके अंतिम उपयोग किए गए मॉडल को याद रखता है"}, "tools": {"title": "उपकरण", "description": "एआई को वेब ब्राउज़ करके, कमांड चलाकर और अधिक समस्याओं को हल करने की अनुमति दें।"}, "customizableModes": {"title": "अनुकूलन योग्य मोड", "description": "विशिष्ट प्रोफाइल अपने व्यवहार और निर्धारित मॉडल के साथ"}, "architect": {"title": "आर्किटेक्ट मोड", "description": "सिस्टम आर्किटेक्चर की योजना बनाएं और कोड संरचना डिज़ाइन करें।"}, "code": {"title": "कोड मोड", "description": "कोड उत्पन्न करें, संशोधित करें और अनुकूलित करें।"}, "unit_test": {"title": "यूनिट टेस्ट मोड", "description": "कोड स्थिरता सुनिश्चित करने के लिए व्यापक परीक्षण बनाएं।"}, "project_fix": {"title": "प्रोजेक्ट फिक्स मोड", "description": "प्रोजेक्ट में समस्याओं या त्रुटियों की पहचान करें और उन्हें ठीक करें।"}, "security_fix": {"title": "सुरक्षा फिक्स मोड", "description": "कोड में सुरक्षा कमजोरियों की पहचान करें और उन्हें हल करें।"}, "code_review": {"title": "कोड समीक्षा मोड", "description": "कोड की गुणवत्ता का विश्लेषण करें और सुधार के सुझाव दें।"}, "documentation": {"title": "प्रलेखन मोड", "description": "स्पष्ट और विस्तृत दस्तावेज़ीकरण और निर्देश बनाएं।"}, "qa": {"title": "प्रश्नोत्तर मोड", "description": "प्रश्नों के उत्तर दें और सरल सहायता प्रदान करें।"}}, "selectMode": "इंटरैक्शन मोड चुनें", "selectApiConfig": "एपीआई कॉन्फ़िगरेशन का चयन करें", "internetSearch": "इंटरनेट खोज खोलने के बाद आप इंटरनेट पर संबंधित सामग्री खोज सकते हैं", "internetSearchClosed": "इंटरनेट खोज बंद करें", "enhancePrompt": "अतिरिक्त संदर्भ के साथ प्रॉम्प्ट बढ़ाएँ", "addImages": "संदेश में चित्र जोड़ें", "sendMessage": "संदेश भेजें", "stopTts": "टेक्स्ट-टू-स्पीच बंद करें", "typeMessage": "एक संदेश लिखें...", "typeTask": "अपना कार्य यहां लिखें...", "addContext": "@ संदर्भ जोड़ें, / मोड स्विच, # शॉर्टकट निर्देश", "dragFiles": "फ़ाइलें खींचने के लिए Shift दबाकर रखें", "dragFilesImages": "फ़ाइलें/चित्र खींचने के लिए Shift दबाकर रखें", "enhancePromptDescription": "'प्रॉम्प्ट बढ़ाएँ' बटन अतिरिक्त संदर्भ, स्पष्टीकरण या पुनर्विचार प्रदान करके आपके अनुरोध को बेहतर बनाने में मदद करता है। यहां अनुरोध लिखकर देखें और यह कैसे काम करता है यह देखने के लिए बटन पर फिर से क्लिक करें।", "modeSelector": {"title": "मोड्स", "marketplace": "मोड मार्केटप्लेस", "settings": "मोड सेटिंग्स", "description": "विशेष व्यक्तित्व जो zhanlu के व्यवहार को अनुकूलित करते हैं।"}, "errorReadingFile": "फ़ाइल पढ़ने में त्रुटि:", "noValidImages": "कोई मान्य चित्र प्रोसेस नहीं किया गया", "separator": "विभाजक", "edit": "संपादित करें...", "forNextMode": "अगले मोड के लिए", "forPreviousMode": "पिछले मोड के लिए", "error": "त्रुटि", "warning": "चेतावनी", "diffError": {"title": "संपादन असफल"}, "troubleMessage": "<PERSON><PERSON><PERSON> को समस्या हो रही है...", "apiRequest": {"title": "API अनुरोध", "failed": "API अनुरोध विफल हुआ", "streaming": "API अनुरोध...", "cancelled": "API अनुरोध रद्द किया गया", "streamingFailed": "API स्ट्रीमिंग विफल हुई"}, "checkpoint": {"initial": "प्रारंभिक चेकपॉइंट", "regular": "चेकपॉइंट", "initializingWarning": "चेकपॉइंट अभी भी आरंभ हो रहा है... अगर यह बहुत समय ले रहा है, तो आप <settingsLink>सेटिंग्स</settingsLink> में चेकपॉइंट को अक्षम कर सकते हैं और अपने कार्य को पुनः आरंभ कर सकते हैं।", "menu": {"viewDiff": "अंतर देखें", "restore": "चेकपॉइंट पुनर्स्थापित करें", "restoreFiles": "फ़ाइलें पुनर्स्थापित करें", "restoreFilesDescription": "आपके प्रोजेक्ट की फ़ाइलों को इस बिंदु पर लिए गए स्नैपशॉट पर पुनर्स्थापित करता है।", "restoreFilesAndTask": "फ़ाइलें और कार्य पुनर्स्थापित करें", "confirm": "पुष्टि करें", "cancel": "रद्<PERSON> करें", "cannotUndo": "इस क्रिया को पूर्ववत नहीं किया जा सकता।", "restoreFilesAndTaskDescription": "आपके प्रोजेक्ट की फ़ाइलों को इस बिंदु पर लिए गए स्नैपशॉट पर पुनर्स्थापित करता है और इस बिंदु के बाद के सभी संदेशों को हटा देता है।"}, "current": "वर्तमान"}, "instructions": {"wantsToFetch": "<PERSON><PERSON><PERSON> को वर्तमान कार्य में सहायता के लिए विस्तृत निर्देश प्राप्त करना है"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON><PERSON> इस फ़ाइल को पढ़ना चाहता है:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON><PERSON> कार्यक्षेत्र के बाहर इस फ़ाइल को पढ़ना चाहता है:", "didRead": "<PERSON><PERSON><PERSON> ने इस फ़ाइल को पढ़ा:", "wantsToEdit": "<PERSON><PERSON><PERSON> इस फ़ाइल को संपादित करना चाहता है:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON><PERSON> कार्यक्षेत्र के बाहर इस फ़ाइल को संपादित करना चाहता है:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> एक सुरक्षित कॉन्फ़िगरेशन फ़ाइल को संपादित करना चाहता है:", "wantsToCreate": "Zhanlu एक नई फ़ाइल बनाना चाहता है:", "wantsToSearchReplace": "<PERSON><PERSON><PERSON> इस फ़ाइल में खोज और प्रतिस्थापन करना चाहता है:", "didSearchReplace": "<PERSON><PERSON><PERSON> ने इस फ़ाइल में खोज और प्रतिस्थापन किया:", "wantsToInsert": "<PERSON>hanlu इस फ़ाइल में सामग्री डालना चाहता है:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> इस फ़ाइल की {{lineNumber}} लाइन पर सामग्री डालना चाहता है:", "wantsToInsertAtEnd": "<PERSON><PERSON><PERSON> इस फ़ाइल के अंत में सामग्री जोड़ना चाहता है:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> इस फ़ाइल को और {{count}} अन्य को पढ़ना चाहता है:", "wantsToReadMultiple": "<PERSON>hanlu कई फ़ाइलें पढ़ना चाहता है:", "wantsToApplyBatchChanges": "<PERSON>hanlu कई फ़ाइलों में परिवर्तन लागू करना चाहता है:"}, "directoryOperations": {"wantsToViewTopLevel": "Zhanlu इस निर्देशिका में शीर्ष स्तर की फ़ाइलें देखना चाहता है:", "didViewTopLevel": "<PERSON><PERSON><PERSON> ने इस निर्देशिका में शीर्ष स्तर की फ़ाइलें देखीं:", "wantsToViewRecursive": "Zhanlu इस निर्देशिका में सभी फ़ाइलों को पुनरावर्ती रूप से देखना चाहता है:", "didViewRecursive": "<PERSON><PERSON><PERSON> ने इस निर्देशिका में सभी फ़ाइलों को पुनरावर्ती रूप से देखा:", "wantsToViewDefinitions": "Zhanlu इस निर्देशिका में उपयोग किए गए सोर्स कोड परिभाषा नामों को देखना चाहता है:", "didViewDefinitions": "<PERSON>han<PERSON> ने इस निर्देशिका में उपयोग किए गए सोर्स कोड परिभाषा नामों को देखा:", "wantsToSearch": "Zhanlu इस निर्देशिका में <code>{{regex}}</code> के लिए खोज करना चाहता है:", "didSearch": "<PERSON><PERSON><PERSON> ने इस निर्देशिका में <code>{{regex}}</code> के लिए खोज की:", "wantsToSearchOutsideWorkspace": "<PERSON>hanlu इस निर्देशिका (कार्यक्षेत्र के बाहर) में <code>{{regex}}</code> के लिए खोज करना चाहता है:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> ने इस निर्देशिका (कार्यक्षेत्र के बाहर) में <code>{{regex}}</code> के लिए खोज की:", "wantsToViewTopLevelOutsideWorkspace": "Zhanlu इस निर्देशिका (कार्यक्षेत्र के बाहर) में शीर्ष स्तर की फ़ाइलें देखना चाहता है:", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> ने इस निर्देशिका (कार्यक्षेत्र के बाहर) में शीर्ष स्तर की फ़ाइलें देखीं:", "wantsToViewRecursiveOutsideWorkspace": "Zhanlu इस निर्देशिका (कार्यक्षेत्र के बाहर) में सभी फ़ाइलों को पुनरावर्ती रूप से देखना चाहता है:", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> ने इस निर्देशिका (कार्यक्षेत्र के बाहर) में सभी फ़ाइलों को पुनरावर्ती रूप से देखा:", "wantsToViewDefinitionsOutsideWorkspace": "Zhanlu इस निर्देशिका (कार्यक्षेत्र के बाहर) में उपयोग किए गए सोर्स कोड परिभाषा नामों को देखना चाहता है:", "didViewDefinitionsOutsideWorkspace": "<PERSON>han<PERSON> ने इस निर्देशिका (कार्यक्षेत्र के बाहर) में उपयोग किए गए सोर्स कोड परिभाषा नामों को देखा:"}, "commandOutput": "कमांड आउटपुट", "commandExecution": {"running": "चलाया जा रहा है", "pid": "पीआईडी: {{pid}}", "exited": "बाहर निकल गया ({{exitCode}})", "manageCommands": "कमांड अनुमतियाँ प्रबंधित करें", "commandManagementDescription": "कमांड अनुमतियों का प्रबंधन करें: स्वतः-निष्पादन की अनुमति देने के लिए ✓ पर क्लिक करें, निष्पादन से इनकार करने के लिए ✗ पर क्लिक करें। पैटर्न को चालू/बंद किया जा सकता है या सूचियों से हटाया जा सकता है। <settingsLink>सभी सेटिंग्स देखें</settingsLink>", "addToAllowed": "अनुमत सूची में जोड़ें", "removeFromAllowed": "अनुमत सूची से हटाएं", "addToDenied": "अस्वीकृत सूची में जोड़ें", "removeFromDenied": "अस्वीकृत सूची से हटाएं", "abortCommand": "कमांड निष्पादन रद्द करें", "expandOutput": "आउटपुट का विस्तार करें", "collapseOutput": "आउटपुट संक्षिप्त करें", "expandManagement": "कमांड प्रबंधन अनुभाग का विस्तार करें", "collapseManagement": "कमांड प्रबंधन अनुभाग संक्षिप्त करें"}, "response": "प्रतिक्रिया", "arguments": "आर्ग्युमेंट्स", "mcp": {"wantsToUseTool": "<PERSON><PERSON><PERSON> {{serverName}} MCP सर्वर पर एक टूल का उपयोग करना चाहता है:", "wantsToAccessResource": "<PERSON><PERSON><PERSON> {{serverName}} MCP सर्वर पर एक संसाधन का उपयोग करना चाहता है:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> <code>{{mode}}</code> मोड में स्विच करना चाहता है", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> <code>{{mode}}</code> मोड में स्विच करना चाहता है क्योंकि: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> <code>{{mode}}</code> मोड में स्विच कर गया", "didSwitchWithReason": "<PERSON><PERSON><PERSON> <code>{{mode}}</code> मोड में स्विच कर गया क्योंकि: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> <code>{{mode}}</code> मोड में एक नया उपकार्य बनाना चाहता है:", "wantsToFinish": "<PERSON>hanlu इस उपकार्य को समाप्त करना चाहता है", "newTaskContent": "उपकार्य निर्देश", "completionContent": "उपकार्य पूर्ण", "resultContent": "उपकार्य परिणाम", "defaultResult": "कृपया अगले कार्य पर जारी रखें।", "completionInstructions": "उपकार्य पूर्ण! आप परिणामों की समीक्षा कर सकते हैं और सुधार या अगले चरण सुझा सकते हैं। यदि सब कुछ ठीक लगता है, तो मुख्य कार्य को परिणाम वापस करने के लिए पुष्टि करें।"}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> का एक प्रश्न है:"}, "taskCompleted": "कार्य पूरा हुआ", "powershell": {"issues": "ऐसा लगता है कि आपको Windows PowerShell के साथ समस्याएँ हो रही हैं, कृपया इसे देखें"}, "autoApprove": {"title": "स्वत:-स्वीकृति:", "none": "कोई नहीं", "description": "स्वत:-स्वीकृति zhan<PERSON> को अनुमति मांगे बिना क्रियाएँ करने की अनुमति देती है। केवल उन क्रियाओं के लिए सक्षम करें जिन पर आप पूरी तरह से विश्वास करते हैं। अधिक विस्तृत कॉन्फ़िगरेशन <settingsLink>सेटिंग्स</settingsLink> में उपलब्ध है।", "selectOptionsFirst": "स्वतः-अनुमोदन सक्षम करने के लिए नीचे दिए گئے विकल्पों में से कम से कम एक का चयन करें", "toggleAriaLabel": "स्वतः-अनुमोदन टॉगल करें", "disabledAriaLabel": "स्वतः-अनुमोदन अक्षम - पहले विकल्प चुनें"}, "reasoning": {"thinking": "विचार कर रहा है", "seconds": "{{count}} सेकंड"}, "contextCondense": {"title": "संदर्भ संक्षिप्त किया गया", "condensing": "संदर्भ संघनित कर रहा है...", "errorHeader": "संदर्भ संघनित करने में विफल", "tokens": "टोकन"}, "followUpSuggest": {"copyToInput": "इनपुट में कॉपी करें (या Shift + क्लिक)", "autoSelectCountdown": "{{count}}s में स्वचालित रूप से चयन हो रहा है", "countdownDisplay": "{{count}}सेकंड"}, "announcement": {"title": "🎉 Zhanlu संस्करण 2.3.2 अपडेट", "description": "बग फिक्स, कोड ऑटो-कम्प्लीशन", "whatsNew": "महत्वपूर्ण अपडेट", "feature1": "<bold>कोड फाउंडेशन मोड जोड़ा गया</bold>: कोड फाउंडेशन मोड प्रोग्रामिंग अभ्यास उत्पन्न करता है", "feature2": "<bold>इतिहास कार्य बग फिक्स</bold>: समस्या हल की गई जहां नए कार्य इतिहास कार्यों में दिखाई देते हैं", "feature3": "<bold>फ्लिकर समस्या फिक्स</bold>: कोड दिखाते समय कभी-कभी स्क्रीन फ्लिकर समस्या हल की गई", "feature4": "<bold>अन्य अनुकूलन</bold>: विभिन्न अन्य समस्याओं के अनुकूलन", "hideButton": "घोषणा छुपाएं", "detailsDiscussLinks": "अधिक फीचर जानने के लिए <discordLink>विस्तृत दस्तावेज़</discordLink> देखें 🚀"}, "browser": {"rooWantsToUse": "<PERSON><PERSON><PERSON> ब्राउज़र का उपयोग करना चाहता है:", "consoleLogs": "कंसोल लॉग", "noNewLogs": "(कोई नया लॉग नहीं)", "screenshot": "ब्राउज़र स्क्रीनशॉट", "cursor": "कर्सर", "navigation": {"step": "चरण {{current}} / {{total}}", "previous": "पिछला", "next": "अगला"}, "sessionStarted": "ब्राउज़र सत्र शुरू हुआ", "actions": {"title": "ब्राउज़र क्रिया: ", "launch": "{{url}} पर ब्राउज़र लॉन्च करें", "click": "क्लिक करें ({{coordinate}})", "type": "टाइप करें \"{{text}}\"", "scrollDown": "नीचे स्क्रॉल करें", "scrollUp": "ऊपर स्क्रॉल करें", "close": "ब्राउज़र बंद करें"}}, "codeblock": {"tooltips": {"expand": "कोड ब्लॉक का विस्तार करें", "collapse": "कोड ब्लॉक को संकुचित करें", "enable_wrap": "वर्ड रैप सक्षम करें", "disable_wrap": "वर्ड रैप अक्षम करें", "copy_code": "कोड कॉपी करें"}}, "qucikInstructions": {"UiToCode": "यूआई डिजाइन चित्र उत्पन्न कोड", "UmlToCode": "UML चित्र उत्पन्न कोड", "ExplainCode": "कोड व्याख्या", "FixCode": "कोड त्रुटि सुधार", "ImproveCode": "कोड अनुकूलन", "UnitTest": "इकाई परीक्षण", "CODE_REVIEW": "कोड समीक्षा", "CommentCode": "कोड टिप्पणी", "PlusButtonClicked": "संवाद खाली करें"}, "systemPromptWarning": "चेतावनी: कस्टम सिस्टम प्रॉम्प्ट ओवरराइड सक्रिय है। यह कार्यक्षमता को गंभीर रूप से बाधित कर सकता है और अनियमित व्यवहार का कारण बन सकता है.", "profileViolationWarning": "वर्तमान प्रोफ़ाइल आपके संगठन की सेटिंग्स के साथ संगत नहीं है", "shellIntegration": {"title": "कमांड निष्पादन चेतावनी", "description": "आपका कमांड VSCode टर्मिनल शेल इंटीग्रेशन के बिना निष्पादित हो रहा है। इस चेतावनी को दबाने के लिए आप <settingsLink>Zhanlu सेटिंग्स</settingsLink> के <strong>Terminal</strong> अनुभाग में शेल इंटीग्रेशन को अक्षम कर सकते हैं या नीचे दिए गए लिंक का उपयोग करके VSCode टर्मिनल इंटीग्रेशन की समस्या का समाधान कर सकते हैं।", "troubleshooting": "शेल इंटीग्रेशन दस्तावेज़ के लिए यहां क्लिक करें।"}, "ask": {"autoApprovedRequestLimitReached": {"title": "स्वत:-स्वीकृत अनुरोध सीमा पहुंची", "description": "z<PERSON><PERSON> {{count}} API अनुरोध(धों) की स्वत:-स्वीकृत सीमा तक पहुंच गया है। क्या आप गणना को रीसेट करके कार्य जारी रखना चाहते हैं?", "button": "रीसेट करें और जारी रखें"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON><PERSON> कोडबेस में <code>{{query}}</code> खोजना चाहता है:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> <code>{{path}}</code> में कोडबेस में <code>{{query}}</code> खोजना चाहता है:", "didSearch_one": "1 परिणाम मिला", "didSearch_other": "{{count}} परिणाम मिले", "resultTooltip": "समानता स्कोर: {{score}} (फ़ाइल खोलने के लिए क्लिक करें)"}, "read-batch": {"approve": {"title": "सभी स्वीकृत करें"}, "deny": {"title": "सभी अस्वीकार करें"}}, "indexingStatus": {"ready": "इंडेक्स तैयार", "indexing": "इंडेक्सिंग {{percentage}}%", "indexed": "इंडेक्स किया गया", "error": "इंडेक्स त्रुटि", "status": "इंडेक्स स्थिति"}, "versionIndicator": {"ariaLabel": "संस्करण {{version}} - रिलीज़ नोट्स देखने के लिए क्लिक करें"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> <PERSON> जल्द आ रहा है!", "description": "क्लाउड में रिमोट एजेंट चलाएं, कहीं से भी अपने कार्यों तक पहुंचें, दूसरों के साथ सहयोग करें, और बहुत कुछ।", "joinWaitlist": "जल्दी पहुंच पाने के लिए प्रतीक्षा सूची में शामिल हों।"}, "editMessage": {"placeholder": "अपना संदेश संपादित करें..."}}