{"errorBoundary": {"title": "Đ<PERSON> xảy ra lỗi", "reportText": "<PERSON><PERSON> lòng gi<PERSON><PERSON> chúng tôi cải thiện bằng cách báo cáo lỗi này trên", "githubText": "trang GitHub Issues của chúng tôi", "copyInstructions": "Sao chép và dán thông báo lỗi sau đây để đưa vào báo cáo của bạn:", "errorStack": "Stack lỗi:", "componentStack": "<PERSON><PERSON> thành phần:"}, "answers": {"yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Xóa", "keep": "Giữ"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "<PERSON><PERSON><PERSON> k<PERSON>..."}, "mermaid": {"loading": "<PERSON><PERSON> tạo biểu đồ mermaid...", "render_error": "<PERSON><PERSON><PERSON><PERSON> thể hiển thị biểu đồ", "file_media": "<PERSON><PERSON> thử quy trình", "code": "<PERSON><PERSON><PERSON> thị mã nguồn", "buttons": {"zoom": "<PERSON><PERSON> ph<PERSON>g", "zoomIn": "<PERSON><PERSON><PERSON> to", "zoomOut": "<PERSON>hu nhỏ", "copy": "Sao chép", "save": "<PERSON><PERSON><PERSON>", "viewCode": "<PERSON><PERSON> mã", "viewDiagram": "<PERSON><PERSON> bi<PERSON> đ<PERSON>", "close": "Đ<PERSON><PERSON>"}, "modal": {"codeTitle": "Mã Mermaid"}, "tabs": {"diagram": "<PERSON><PERSON><PERSON><PERSON> đồ", "code": "Mã"}, "feedback": {"imageCopied": "<PERSON><PERSON>nh ảnh đã đư<PERSON>c sao chép vào clipboard", "copyError": "Lỗi sao chép hình <PERSON>nh"}}, "file": {"errors": {"invalidDataUri": "<PERSON><PERSON><PERSON> dạng URI dữ liệu không hợp lệ", "copyingImage": "Lỗi sao chép hình ảnh: {{error}}", "openingImage": "Lỗi mở hình ảnh: {{error}}", "pathNotExists": "Đường dẫn không tồn tại: {{path}}", "couldNotOpen": "<PERSON><PERSON><PERSON><PERSON> thể mở tệp: {{error}}", "couldNotOpenGeneric": "<PERSON>h<PERSON>ng thể mở tệp!"}, "success": {"imageDataUriCopied": "URI dữ liệu hình ảnh đã đư<PERSON>c sao chép vào clipboard"}}, "confirmation": {"deleteMessage": "<PERSON><PERSON><PERSON>", "deleteWarning": "Xóa tin nhắn này sẽ xóa tất cả các tin nhắn tiếp theo trong cuộc trò chuyện. Bạn có muốn tiếp tục không?", "editMessage": "Chỉnh <PERSON><PERSON><PERSON>", "editWarning": "Chỉnh sửa tin nhắn này sẽ xóa tất cả các tin nhắn tiếp theo trong cuộc trò chuyện. Bạn có muốn tiếp tục không?", "proceed": "<PERSON><PERSON><PERSON><PERSON>"}}