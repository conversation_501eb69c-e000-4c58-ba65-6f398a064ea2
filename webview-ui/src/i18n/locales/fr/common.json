{"errorBoundary": {"title": "Une erreur s'est produite", "reportText": "Aidez-nous à nous améliorer en signalant cette erreur sur", "githubText": "notre page GitHub Issues", "copyInstructions": "<PERSON><PERSON><PERSON> et collez le message d'erreur suivant pour l'inclure dans votre rapport :", "errorStack": "<PERSON><PERSON> d'er<PERSON> :", "componentStack": "<PERSON><PERSON> de composants :"}, "answers": {"yes": "O<PERSON>", "no": "Non", "cancel": "Annuler", "remove": "<PERSON><PERSON><PERSON><PERSON>", "keep": "Conserver"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "ui": {"search_placeholder": "Rechercher..."}, "mermaid": {"loading": "Génération du diagramme mermaid...", "render_error": "Impossible de rendre le diagramme", "file_media": "Aperçu du processus", "code": "Affichage du code source", "buttons": {"zoom": "Zoom", "zoomIn": "<PERSON><PERSON><PERSON><PERSON>", "zoomOut": "<PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "save": "Enregistrer l'image", "viewCode": "Voir le code", "viewDiagram": "Voir le diagramme", "close": "<PERSON><PERSON><PERSON>"}, "modal": {"codeTitle": "Code Mermaid"}, "tabs": {"diagram": "Diagramme", "code": "Code"}, "feedback": {"imageCopied": "Image copiée dans le presse-papiers", "copyError": "Erreur lors de la copie de l'image"}}, "file": {"errors": {"invalidDataUri": "Format d'URI de données invalide", "copyingImage": "Erreur lors de la copie de l'image : {{error}}", "openingImage": "Erreur lors de l'ouverture de l'image : {{error}}", "pathNotExists": "Le chemin n'existe pas : {{path}}", "couldNotOpen": "Impossible d'ouvrir le fichier : {{error}}", "couldNotOpenGeneric": "Impossible d'ouvrir le fichier !"}, "success": {"imageDataUriCopied": "URI de données d'image copiée dans le presse-papiers"}}, "confirmation": {"deleteMessage": "Supprimer le message", "deleteWarning": "Supprimer ce message supprimera tous les messages suivants dans la conversation. Voulez-vous continuer ?", "editMessage": "Modifier le message", "editWarning": "Modifier ce message supprimera tous les messages suivants dans la conversation. Voulez-vous continuer ?", "proceed": "<PERSON><PERSON><PERSON>"}}