{"greeting": "Bienvenue sur Zhanlu", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Voir plus", "seeLess": "Voir moins", "tokens": "Tokens :", "cache": "Cache :", "apiCost": "Coût API :", "contextWindow": "<PERSON><PERSON><PERSON> du contexte :", "closeAndStart": "<PERSON><PERSON><PERSON> la tâche et en commencer une nouvelle", "export": "Exporter l'historique des tâches", "delete": "Supprimer la tâche (Shift + Clic pour ignorer la confirmation)", "condenseContext": "Condenser intelligemment le contexte", "share": "Partager la tâche", "copyId": "Copier l'ID de la tâche", "shareWithOrganization": "Partager avec l'organisation", "shareWithOrganizationDescription": "Seuls les membres de ton organisation peuvent accéder", "sharePublicly": "Partager publiquement", "sharePubliclyDescription": "Toute personne avec le lien peut accéder", "connectToCloud": "Se connecter au Cloud", "connectToCloudDescription": "Connecte-toi à zhanlu Cloud pour partager des tâches", "sharingDisabledByOrganization": "Partage désactivé par l'organisation", "shareSuccessOrganization": "Lien d'organisation copié dans le presse-papiers", "shareSuccessPublic": "Lien public copié dans le presse-papiers"}, "unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pin": "<PERSON><PERSON><PERSON>", "tokenProgress": {"availableSpace": "Espace disponible : {{amount}} tokens", "tokensUsed": "Tokens utilisés : {{used}} sur {{total}}", "reservedForResponse": "Réservé pour la réponse du modèle : {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Tenter à nouveau l'opération"}, "startNewTask": {"title": "Commencer une nouvelle tâche", "tooltip": "<PERSON><PERSON><PERSON><PERSON> une nouvelle tâche"}, "proceedAnyways": {"title": "Continuer quand même", "tooltip": "Continuer pendant l'exécution de la commande"}, "save": {"title": "Enregistrer", "tooltip": "Enregistrer les modifications du message"}, "reject": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Rejeter cette action"}, "completeSubtaskAndReturn": "Terminer la sous-tâche et revenir", "approve": {"title": "Approuver", "tooltip": "Approuver cette action"}, "runCommand": {"title": "Exécuter la commande", "tooltip": "Exécuter cette commande"}, "proceedWhileRunning": {"title": "Continuer pendant l'exécution", "tooltip": "Continuer malgré les avertissements"}, "killCommand": {"title": "<PERSON><PERSON><PERSON><PERSON> la commande", "tooltip": "<PERSON><PERSON><PERSON><PERSON> la commande actuelle"}, "resumeTask": {"title": "Reprendre la tâche", "tooltip": "Continuer la tâche actuelle"}, "terminate": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Te<PERSON>iner la tâche actuelle"}, "cancel": {"title": "Annuler", "tooltip": "Annuler l'opération actuelle"}, "scrollToBottom": "<PERSON><PERSON><PERSON><PERSON> jusqu'au bas du chat", "about": "<PERSON><PERSON><PERSON><PERSON>, co<PERSON><PERSON><PERSON>, refactor<PERSON>z et déboguez du code avec l'aide du modèle IA Zhanlu.<br />Consultez notre <DocsLink>documentation</DocsLink> pour plus d'informations.", "onboarding": "La liste des tâches dans cet espace de travail est vide. Commencez en entrant votre tâche ci-dessous.<br>Vous ne savez pas par où commencer ? Lisez notre <DocsLink>documentation</DocsLink>.", "zhanluTips": {"architectMode": {"title": "Mode Architecte", "description": "Crée des plans détaillés pour implémenter des solutions"}, "codeMode": {"title": "Mode Code", "description": "Écrit et modifie du code selon les meilleures pratiques"}, "testMode": {"title": "Mode Test Unitaire", "description": "Génère des tests complets pour votre code"}, "projectFixMode": {"title": "Mode Correction de Projet", "description": "Identifie et répare les défauts dans les projets"}, "sastMode": {"title": "Mode Correction de Sécurité", "description": "Corrige les vulnérabilités de sécurité dans le code"}, "codeReviewMode": {"title": "Mode Revue de Code", "description": "Évalue la qualité du code et suggère des améliorations"}, "readmeMode": {"title": "Mode Documentation", "description": "Crée une documentation technique détaillée"}, "simpleMode": {"title": "Mode Questions-Réponses", "description": "Répond précisément aux questions techniques"}, "boomerangTasks": {"title": "Orchestration de Tâches", "description": "Divisez les tâches en parties plus petites et gérables."}, "stickyModels": {"title": "Modes persistants", "description": "Chaque mode se souvient de votre dernier modèle utilisé"}, "tools": {"title": "Outils", "description": "Permettez à l'IA de résoudre des problèmes en naviguant sur le Web, en exécutant des commandes, et plus encore."}, "customizableModes": {"title": "Modes personnalisables", "description": "Des personas spécialisés avec leurs propres comportements et modèles assignés"}}, "selectMode": "Sélectionner le mode d'interaction", "selectApiConfig": "Sélectionner la configuration de l'API", "internetSearch": "Après avoir activé la récupération Internet, vous pouvez rechercher du contenu pertinent sur Internet", "internetSearchClosed": "Fermer la récupération Internet", "enhancePrompt": "Améliorer la requête avec un contexte supplémentaire", "addImages": "Ajouter des images au message", "sendMessage": "Envoyer le message", "stopTts": "<PERSON><PERSON><PERSON><PERSON> la synthèse vocale", "typeMessage": "Écrivez un message...", "typeTask": "Écrivez votre tâche ici...", "addContext": "@ ajouter un contexte, / changer de mode, # instructions de raccourci", "dragFiles": "Maintenez Shift et faites glisser des fichiers", "dragFilesImages": "Maintenez Shift et faites glisser des fichiers/images", "enhancePromptDescription": "Le bouton 'Améliorer la requête' aide à améliorer votre demande en fournissant un contexte supplémentaire, des clarifications ou des reformulations. Essayez de taper une demande ici et cliquez à nouveau sur le bouton pour voir comment cela fonctionne.", "modeSelector": {"title": "Modes", "marketplace": "Marketplace de Modes", "settings": "Paramètres des Modes", "description": "Personas spécialisés qui adaptent le comportement de Roo."}, "errorReadingFile": "<PERSON><PERSON>ur lors de la lecture du fichier :", "noValidImages": "Aucune image valide n'a été traitée", "separator": "Séparateur", "edit": "Éditer...", "forNextMode": "pour le prochain mode", "forPreviousMode": "pour le mode précédent", "error": "<PERSON><PERSON><PERSON>", "warning": "Avertissement", "diffError": {"title": "Modification échouée"}, "troubleMessage": "<PERSON><PERSON>lu rencontre des difficultés...", "apiRequest": {"title": "Requête API", "failed": "Échec de la requête API", "streaming": "Requête API...", "cancelled": "Requête API annulée", "streamingFailed": "Échec du streaming API"}, "checkpoint": {"initial": "Point de contrôle initial", "regular": "Point de contrôle", "initializingWarning": "Initialisation du point de contrôle en cours... Si cela prend trop de temps, tu peux désactiver les points de contrôle dans les <settingsLink>paramètres</settingsLink> et redémarrer ta tâche.", "menu": {"viewDiff": "Voir les différences", "restore": "Restaurer le point de contrôle", "restoreFiles": "Restaurer les fichiers", "restoreFilesDescription": "Restaure les fichiers de votre projet à un instantané pris à ce moment.", "restoreFilesAndTask": "Restaurer fichiers et tâche", "confirm": "Confirmer", "cancel": "Annuler", "cannotUndo": "Cette action ne peut pas être annulée.", "restoreFilesAndTaskDescription": "Restaure les fichiers de votre projet à un instantané pris à ce moment et supprime tous les messages après ce point."}, "current": "Actuel"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON><PERSON> veut lire ce fichier :", "wantsToReadOutsideWorkspace": "<PERSON><PERSON><PERSON> veut lire ce fichier en dehors de l'espace de travail :", "didRead": "<PERSON><PERSON><PERSON> a lu ce fichier :", "wantsToEdit": "<PERSON><PERSON><PERSON> veut éditer ce fichier :", "wantsToEditOutsideWorkspace": "<PERSON><PERSON><PERSON> veut éditer ce fichier en dehors de l'espace de travail :", "wantsToEditProtected": "<PERSON><PERSON><PERSON> veut éditer un fichier de configuration protégé :", "wantsToCreate": "<PERSON><PERSON><PERSON> veut créer un nouveau fichier :", "wantsToSearchReplace": "<PERSON><PERSON><PERSON> veut effectuer une recherche et remplacement sur ce fichier :", "didSearchReplace": "<PERSON><PERSON><PERSON> a effectué une recherche et remplacement sur ce fichier :", "wantsToInsert": "<PERSON><PERSON><PERSON> veut insérer du contenu dans ce fichier :", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> veut insérer du contenu dans ce fichier à la ligne {{lineNumber}} :", "wantsToInsertAtEnd": "<PERSON><PERSON><PERSON> veut ajouter du contenu à la fin de ce fichier :", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> veut lire ce fichier et {{count}} de plus :", "wantsToReadMultiple": "<PERSON><PERSON><PERSON> souhaite lire plusieurs fichiers :", "wantsToApplyBatchChanges": "<PERSON><PERSON><PERSON> veut appliquer des modifications à plusieurs fichiers :"}, "instructions": {"wantsToFetch": "<PERSON><PERSON><PERSON> veut récupérer des instructions détaillées pour aider à la tâche actuelle"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON><PERSON> veut voir les fichiers de premier niveau dans ce répertoire :", "didViewTopLevel": "<PERSON><PERSON><PERSON> a vu les fichiers de premier niveau dans ce répertoire :", "wantsToViewRecursive": "<PERSON><PERSON><PERSON> veut voir récursivement tous les fichiers dans ce répertoire :", "didViewRecursive": "<PERSON><PERSON><PERSON> a vu récursivement tous les fichiers dans ce répertoire :", "wantsToViewDefinitions": "<PERSON><PERSON>lu veut voir les noms de définitions de code source utilisés dans ce répertoire :", "didViewDefinitions": "Zhanlu a vu les noms de définitions de code source utilisés dans ce répertoire :", "wantsToSearch": "<PERSON><PERSON><PERSON> veut rechercher dans ce répertoire <code>{{regex}}</code> :", "didSearch": "<PERSON><PERSON><PERSON> a recherché dans ce répertoire <code>{{regex}}</code> :", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> veut rechercher dans ce répertoire (hors espace de travail) <code>{{regex}}</code> :", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> a recherché dans ce répertoire (hors espace de travail) <code>{{regex}}</code> :", "wantsToViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> veut voir les fichiers de premier niveau dans ce répertoire (hors espace de travail) :", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> a vu les fichiers de premier niveau dans ce répertoire (hors espace de travail) :", "wantsToViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> veut voir récursivement tous les fichiers dans ce répertoire (hors espace de travail) :", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> a vu récursivement tous les fichiers dans ce répertoire (hors espace de travail) :", "wantsToViewDefinitionsOutsideWorkspace": "<PERSON><PERSON>lu veut voir les noms de définitions de code source utilisés dans ce répertoire (hors espace de travail) :", "didViewDefinitionsOutsideWorkspace": "Zhanlu a vu les noms de définitions de code source utilisés dans ce répertoire (hors espace de travail) :"}, "commandOutput": "Sortie de commande", "commandExecution": {"running": "En cours d'exécution", "pid": "PID : {{pid}}", "exited": "Terminé ({{exitCode}})", "manageCommands": "<PERSON><PERSON>rer les autorisations de commande", "commandManagementDescription": "Gérer les autorisations de commande : Cliquez sur ✓ pour autoriser l'exécution automatique, ✗ pour refuser l'exécution. Les modèles peuvent être activés/désactivés ou supprimés des listes. <settingsLink>Voir tous les paramètres</settingsLink>", "addToAllowed": "Ajouter à la liste autorisée", "removeFromAllowed": "Retirer de la liste autorisée", "addToDenied": "A<PERSON>ter à la liste refusée", "removeFromDenied": "<PERSON><PERSON><PERSON> de la liste refusée", "abortCommand": "Abandonner l'exécution de la commande", "expandOutput": "Développer la sortie", "collapseOutput": "Réduire la sortie", "expandManagement": "Développer la section de gestion des commandes", "collapseManagement": "Réduire la section de gestion des commandes"}, "response": "Réponse", "arguments": "Arguments", "mcp": {"wantsToUseTool": "<PERSON><PERSON><PERSON> veut utiliser un outil sur le serveur MCP {{serverName}} :", "wantsToAccessResource": "<PERSON><PERSON><PERSON> veut accéder à une ressource sur le serveur MCP {{serverName}} :"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> veut passer au mode <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> veut passer au mode <code>{{mode}}</code> car : {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> est passé au mode <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON><PERSON> est passé au mode <code>{{mode}}</code> car : {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> veut créer une nouvelle sous-tâche en mode <code>{{mode}}</code> :", "wantsToFinish": "<PERSON><PERSON><PERSON> veut terminer cette sous-tâche", "newTaskContent": "Instructions de la sous-tâche", "completionContent": "Sous-tâche terminée", "resultContent": "Résultats de la sous-tâche", "defaultResult": "Veuillez continuer avec la tâche suivante.", "completionInstructions": "Sous-tâche terminée ! Vous pouvez examiner les résultats et suggérer des corrections ou les prochaines étapes. Si tout semble bon, confirmez pour retourner le résultat à la tâche parente."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> a une question :"}, "taskCompleted": "Tâche terminée", "powershell": {"issues": "Il semble que vous rencontriez des problèmes avec Windows PowerShell, ve<PERSON><PERSON>z consulter ce"}, "autoApprove": {"title": "Auto-approbation :", "none": "Aucune", "description": "L'auto-approbation permet à zhanlu d'effectuer des actions sans demander d'autorisation. Activez-la uniquement pour les actions auxquelles vous faites entièrement confiance. Configuration plus détaillée disponible dans les <settingsLink>Paramètres</settingsLink>.", "selectOptionsFirst": "Sélectionnez au moins une option ci-dessous pour activer l'auto-approbation", "toggleAriaLabel": "Activer/désactiver l'approbation automatique", "disabledAriaLabel": "Approbation automatique désactivée - sélectionnez d'abord les options"}, "reasoning": {"thinking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contexte condensé", "condensing": "Condensation du contexte...", "errorHeader": "Échec de la condensation du contexte", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "<PERSON><PERSON>r vers l'entrée (ou Shift + clic)", "autoSelectCountdown": "Sélection automatique dans {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Mise à jour <PERSON> 2.3.2", "description": "Correction de bugs, complétion de code", "whatsNew": "Mises à jour importantes", "feature1": "<bold>Mode Fondation de Code ajouté</bold> : Le mode Fondation de Code génère des exercices de programmation", "feature2": "<bold>Correction du bug des tâches d'historique</bold> : Problème résolu où les nouvelles tâches apparaissent dans les tâches d'historique", "feature3": "<bold>Correction du problème de scintillement</bold> : Problème de scintillement occasionnel de l'écran lors de l'affichage du code résolu", "feature4": "<bold>Autres optimisations</bold> : Optimisations de divers autres problèmes", "hideButton": "Masquer l'annonce", "detailsDiscussLinks": "Consultez la <discordLink>documentation détaillée</discordLink> pour découvrir plus de fonctionnalités 🚀"}, "browser": {"rooWantsToUse": "<PERSON><PERSON><PERSON> veut utiliser le navigateur :", "consoleLogs": "Journaux de console", "noNewLogs": "(Pas de nouveaux journaux)", "screenshot": "Capture d'écran du navigateur", "cursor": "<PERSON><PERSON>", "navigation": {"step": "Étape {{current}} sur {{total}}", "previous": "Précédent", "next": "Suivant"}, "sessionStarted": "Session de navigateur démar<PERSON>e", "actions": {"title": "Action de navigation : ", "launch": "<PERSON><PERSON> le <PERSON> sur {{url}}", "click": "Cliquer ({{coordinate}})", "type": "<PERSON><PERSON> \"{{text}}\"", "scrollDown": "Dé<PERSON>ler vers le bas", "scrollUp": "Dé<PERSON><PERSON> vers le haut", "close": "<PERSON><PERSON><PERSON> le <PERSON>ur"}}, "codeblock": {"tooltips": {"expand": "Développer le bloc de code", "collapse": "Réduire le bloc de code", "enable_wrap": "<PERSON><PERSON> le retour à la ligne", "disable_wrap": "Désactiver le retour à la ligne", "copy_code": "Copier le code"}}, "qucikInstructions": {"UiToCode": "Diagramme de conception UI pour générer du Code", "UmlToCode": "Code de génération de diagramme UML", "ExplainCode": "Explication du Code", "FixCode": "Correction d'erreur de code", "ImproveCode": "Optimisation du Code", "UnitTest": "Tests unitaires", "CODE_REVIEW": "Revue du Code", "CommentCode": "Commentaire de code", "PlusButtonClicked": "Vider la boîte de dialogue"}, "systemPromptWarning": "AVERTISSEMENT : Remplacement d'instructions système personnalisées actif. Cela peut gravement perturber la fonctionnalité et provoquer un comportement imprévisible.", "profileViolationWarning": "Le profil actuel n'est pas compatible avec les paramètres de votre organisation", "shellIntegration": {"title": "Avertissement d'exécution de commande", "description": "Votre commande est exécutée sans l'intégration shell du terminal VSCode. Pour supprimer cet avertissement, vous pouvez désactiver l'intégration shell dans la section <strong>Terminal</strong> des <settingsLink>paramètres de Zhanlu</settingsLink> ou résoudre les problèmes d'intégration du terminal VSCode en utilisant le lien ci-dessous.", "troubleshooting": "Cliquez ici pour la documentation d'intégration shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limite de requêtes auto-approuvées atteinte", "description": "zhan<PERSON> a atteint la limite auto-approuvée de {{count}} requête(s) API. Souhaitez-vous réinitialiser le compteur et poursuivre la tâche ?", "button": "Réinitialiser et continuer"}}, "codebaseSearch": {"wantsToSearch": "z<PERSON><PERSON> veut rechercher dans la base de code <code>{{query}}</code> :", "wantsToSearchWithPath": "z<PERSON><PERSON> veut rechercher dans la base de code <code>{{query}}</code> dans <code>{{path}}</code> :", "didSearch_one": "1 résultat trouvé", "didSearch_other": "{{count}} résultats trouvés", "resultTooltip": "Score de similarité : {{score}} (cliquer pour ouvrir le fichier)"}, "read-batch": {"approve": {"title": "Tout approuver"}, "deny": {"title": "Tout refuser"}}, "indexingStatus": {"ready": "Index prêt", "indexing": "Indexation {{percentage}}%", "indexed": "Indexé", "error": "Erreur d'index", "status": "Statut de l'index"}, "versionIndicator": {"ariaLabel": "Version {{version}} - Cliquez pour voir les notes de version"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> Cloud arrive bientôt !", "description": "Exécutez des agents distants dans le cloud, accédez à vos tâches de n'importe où, collaborez avec d'autres et bien plus encore.", "joinWaitlist": "Rejoignez la liste d'attente pour obtenir un accès anticipé."}, "editMessage": {"placeholder": "Modifiez votre message..."}}