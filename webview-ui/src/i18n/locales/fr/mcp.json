{"title": "Serveurs MCP", "done": "<PERSON><PERSON><PERSON><PERSON>", "marketplace": "<PERSON><PERSON>", "description": "Le <0>Model Context Protocol</0> permet la communication avec des serveurs MCP exécutés localement qui fournissent des outils et des ressources supplémentaires pour étendre les capacités de Roo. Vous pouvez utiliser <1>des serveurs créés par la communauté</1> ou demander à zhanlu de créer de nouveaux outils spécifiques à votre flux de travail (par exemple, \"ajouter un outil qui récupère la dernière documentation npm\").", "instructions": "Instructions", "enableToggle": {"title": "Activer les serveurs MCP", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> pourra interagir avec les serveurs MCP pour des fonctionnalités avancées. Si vous n'utilisez pas MCP, vous pouvez désactiver cette option pour réduire l'utilisation de tokens par <PERSON><PERSON><PERSON>."}, "enableServerCreation": {"title": "Activer la création de serveurs MCP", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> peut vous aider à créer de nouveaux serveurs MCP via des commandes comme \"ajouter un nouvel outil pour...\". Si vous n'avez pas besoin de créer des serveurs MCP, vous pouvez désactiver cette option pour réduire l'utilisation de tokens par <PERSON><PERSON><PERSON>.", "hint": "Astuce : Pour réduire les coûts de tokens API, désactive cette option quand tu ne demandes pas à zhanlu de créer un nouveau serveur MCP."}, "editGlobalMCP": "Modifier MCP Global", "editProjectMCP": "Modifier MCP du Projet", "whatIsMcp": "Qu'est-ce que MCP Server", "learnMoreEditingSettings": "En savoir plus sur la modification des fichiers de configuration MCP", "tool": {"alwaysAllow": "Toujours autoriser", "parameters": "Paramètres", "noDescription": "Aucune description", "togglePromptInclusion": "Basculer l'inclusion dans le prompt"}, "tabs": {"tools": "Outils", "resources": "Ressources", "errors": "<PERSON><PERSON><PERSON>"}, "emptyState": {"noTools": "<PERSON><PERSON><PERSON> outil trouvé", "noResources": "<PERSON><PERSON><PERSON> ressource trouvée", "noErrors": "<PERSON><PERSON><PERSON> erreur trouvée"}, "networkTimeout": {"label": "<PERSON><PERSON><PERSON>'attente r<PERSON>", "description": "Temps d'attente maximal pour les réponses du serveur", "options": {"15seconds": "15 secondes", "30seconds": "30 secondes", "1minute": "1 minute", "5minutes": "5 minutes", "10minutes": "10 minutes", "15minutes": "15 minutes", "30minutes": "30 minutes", "60minutes": "60 minutes"}}, "deleteDialog": {"title": "Supp<PERSON>er le serveur MCP", "description": "Es-tu sûr de vouloir supprimer le serveur MCP \"{{serverName}}\" ? Cette action est irréversible.", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "serverStatus": {"retrying": "Nouvelle tentative...", "retryConnection": "Réessayer la connexion"}, "refreshMCP": "Ra<PERSON><PERSON><PERSON>r les serveurs MCP", "execution": {"running": "En cours", "completed": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON>"}}