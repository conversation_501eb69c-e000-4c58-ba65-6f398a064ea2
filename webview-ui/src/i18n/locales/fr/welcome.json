{"greeting": "Bienvenue sur zhanlu !", "introduction": "Avec une gamme de Modes intégrés et extensibles, zhanlu te permet de planifier, architecturer, coder, déboguer et augmenter ta productivité comme jamais auparavant.", "notice": "Pour commencer, cette extension a besoin d'un fournisseur d'API.", "start": "C'est parti !", "routers": {"requesty": {"description": "Ton routeur LLM optimisé", "incentive": "1$ de crédit gratuit"}, "openrouter": {"description": "Une interface unifiée pour les LLMs"}}, "chooseProvider": "Pour faire sa magie, zhan<PERSON> a besoin d'une clé API.", "startRouter": "Nous recommandons d'utiliser un routeur LLM :", "startCustom": "Ou tu peux apporter ta propre clé API :", "telemetry": {"title": "Aide à améliorer <PERSON>", "anonymousTelemetry": "Envoie des données d'utilisation et d'erreurs anonymes pour nous aider à corriger les bugs et améliorer l'extension. Aucun code, texte ou information personnelle n'est jamais envoyé.", "changeSettings": "Tu peux toujours modifier cela en bas des <settingsLink>paramètres</settingsLink>", "settings": "paramètres", "allow": "Autoriser", "deny": "Refuser"}, "importSettings": "Importer les paramètres"}