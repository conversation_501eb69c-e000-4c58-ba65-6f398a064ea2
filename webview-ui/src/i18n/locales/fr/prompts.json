{"title": "Modes", "done": "<PERSON><PERSON><PERSON><PERSON>", "modes": {"title": "Modes", "createNewMode": "Créer un nouveau mode", "importMode": "Importer le mode", "noMatchFound": "Aucun mode trouvé", "editModesConfig": "Modifier la configuration des modes", "editGlobalModes": "Modifier les modes globaux", "editProjectModes": "Modifier les modes du projet (.z<PERSON><PERSON><PERSON>)", "createModeHelpText": "Cliquez sur + pour créer un nouveau mode personnalisé, ou demandez simplement à Zhanlu dans le chat de vous en créer un !", "selectMode": "Rechercher les modes"}, "apiConfiguration": {"title": "Configuration API", "select": "Sélectionnez la configuration API à utiliser pour ce mode"}, "tools": {"title": "Outils disponibles", "builtInModesText": "Les outils pour les modes intégrés ne peuvent pas être modifiés", "editTools": "Modifier les outils", "doneEditing": "Terminer <PERSON>", "allowedFiles": "Fichiers autorisés :", "toolNames": {"read": "<PERSON>re les fichiers", "edit": "Modifier les fichiers", "browser": "Utiliser le navigateur", "command": "Exécuter des commandes", "mcp": "Utiliser MCP"}, "noTools": "Aucun"}, "roleDefinition": {"title": "Définition du rôle", "resetToDefault": "Réinitialiser aux valeurs par défaut", "description": "Définissez l'expertise et la personnalité de zhanlu pour ce mode. Cette description façonne la manière dont zhanlu se présente et aborde les tâches."}, "description": {"title": "Description courte (pour humains)", "resetToDefault": "Réinitialiser à la description par défaut", "description": "Une brève description affichée dans le menu déroulant du sélecteur de mode."}, "whenToUse": {"title": "Quand utiliser (optionnel)", "description": "Décrivez quand ce mode doit être utilisé. <PERSON><PERSON> aide l'Orchestrateur à choisir le mode approprié pour une tâche.", "resetToDefault": "Réinitialiser la description 'Quand utiliser' aux valeurs par défaut"}, "customInstructions": {"title": "Instructions personnalisées spécifiques au mode (optionnel)", "resetToDefault": "Réinitialiser aux valeurs par défaut", "description": "Ajoutez des directives comportementales spécifiques au mode {{modeName}}.", "loadFromFile": "Les instructions personnalisées spécifiques au mode {{mode}} peuvent également être chargées depuis le dossier <span>.zhanlu/rules-{{slug}}/</span> dans votre espace de travail (.zhan<PERSON><PERSON><PERSON>-{{slug}} et .clinerules-{{slug}} sont obsolètes et cesseront de fonctionner bientôt)."}, "exportMode": {"title": "Exporter le mode", "description": "Exporte ce mode vers un fichier YAML avec toutes les règles incluses pour un partage facile avec d'autres.", "export": "Exporter le mode", "exporting": "Exportation..."}, "importMode": {"selectLevel": "Choisissez où importer ce mode :", "import": "Importer", "importing": "Importation...", "global": {"label": "Niveau global", "description": "Disponible dans tous les projets. Si le mode exporté contenait des fichiers de règles, ils seront recréés dans le dossier global .zhanlu/rules-{slug}/."}, "project": {"label": "Niveau projet", "description": "Disponible uniquement dans cet espace de travail. Si le mode exporté contenait des fichiers de règles, ils seront recréés dans le dossier .zhanlu/rules-{slug}/."}}, "advanced": {"title": "<PERSON><PERSON><PERSON>"}, "globalCustomInstructions": {"title": "Instructions personnalisées pour tous les modes", "description": "Ces instructions s'appliquent à tous les modes. Elles fournissent un ensemble de comportements de base qui peuvent être améliorés par des instructions spécifiques au mode ci-dessous.\nSi vous souhaitez que <PERSON> pense et parle dans une langue différente de celle de votre éditeur ({{language}}), vous pouvez le spécifier ici.", "loadFromFile": "Les instructions peuvent également être chargées depuis le dossier <span>.zhanlu/rules/</span> dans votre espace de travail."}, "systemPrompt": {"preview": "Aperçu du prompt système", "copy": "<PERSON><PERSON><PERSON> le prompt système dans le presse-papiers", "title": "Prompt système (mode {{modeName}})"}, "supportPrompts": {"title": "Prompts de support", "resetPrompt": "Réinitialiser le prompt {{promptType}} aux valeurs par défaut", "prompt": "Prompt", "enhance": {"apiConfiguration": "Configuration API", "apiConfigDescription": "Vous pouvez sélectionner une configuration API à toujours utiliser pour améliorer les prompts, ou simplement utiliser celle qui est actuellement sélectionnée", "useCurrentConfig": "Utiliser la configuration API actuellement sélectionnée", "testPromptPlaceholder": "Entrez un prompt pour tester l'amélioration", "previewButton": "Aperçu de l'amélioration du prompt", "testEnhancement": "Tester l'amélioration"}, "condense": {"apiConfiguration": "Configuration de l'API pour la condensation du contexte", "apiConfigDescription": "Sélectionnez la configuration d'API à utiliser pour les opérations de condensation de contexte. Laissez non sélectionné pour utiliser la configuration active actuelle.", "useCurrentConfig": "Utiliser la configuration d'API actuellement sélectionnée"}, "types": {"ENHANCE": {"label": "Amé<PERSON><PERSON> le prompt", "description": "Utilisez l'amélioration de prompt pour obtenir des suggestions ou des améliorations personnalisées pour vos entrées. <PERSON><PERSON> garan<PERSON>t que zhanlu comprend votre intention et fournit les meilleures réponses possibles. Disponible via l'icône ✨ dans le chat."}, "CONDENSE": {"label": "Condensation du contexte", "description": "Configurez la manière dont le contexte de la conversation est condensé pour gérer les limites de jetons. Ce prompt est utilisé pour les opérations de condensation de contexte manuelles et automatiques."}, "EXPLAIN": {"label": "Expliquer le code", "description": "Obtenez des explications détaillées sur des extraits de code, des fonctions ou des fichiers entiers. Utile pour comprendre un code complexe ou apprendre de nouveaux modèles. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "FIX": {"label": "Corriger les problèmes", "description": "Obtenez de l'aide pour identifier et résoudre les bugs, les erreurs ou les problèmes de qualité du code. Fournit des conseils étape par étape pour résoudre les problèmes. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "IMPROVE": {"label": "Améliorer le code", "description": "Recevez des suggestions pour l'optimisation du code, de meilleures pratiques et des améliorations architecturales tout en maintenant la fonctionnalité. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "ADD_TO_CONTEXT": {"label": "A<PERSON>ter au contexte", "description": "Ajoutez du contexte à votre tâche ou conversation actuelle. Utile pour fournir des informations supplémentaires ou des clarifications. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "TERMINAL_ADD_TO_CONTEXT": {"label": "Ajouter le contenu du terminal au contexte", "description": "Ajoutez la sortie du terminal à votre tâche ou conversation actuelle. Utile pour fournir des sorties de commandes ou des journaux. Disponible dans le menu contextuel du terminal (clic droit sur le contenu sélectionné du terminal)."}, "TERMINAL_FIX": {"label": "Corriger la commande du terminal", "description": "Obt<PERSON><PERSON> de l'aide pour corriger les commandes du terminal qui ont échoué ou qui nécessitent des améliorations. Disponible dans le menu contextuel du terminal (clic droit sur le contenu sélectionné du terminal)."}, "TERMINAL_EXPLAIN": {"label": "Expliquer la commande du terminal", "description": "Obtenez des explications détaillées sur les commandes du terminal et leurs sorties. Disponible dans le menu contextuel du terminal (clic droit sur le contenu sélectionné du terminal)."}, "NEW_TASK": {"label": "<PERSON><PERSON><PERSON><PERSON> une nouvelle tâche", "description": "<PERSON><PERSON><PERSON>re une nouvelle tâche avec ton entrée. Disponible dans la palette de commandes."}, "UNIT_TEST": {"label": "Test unitaire", "description": "Générer des tests unitaires complets pour le code sélectionné. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "CODE_REVIEW": {"label": "Revue de code", "description": "Effectuer une revue de code détaillée et fournir des suggestions d'amélioration pour le code sélectionné. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}, "COMMENT_CODE": {"label": "Commenter le code", "description": "Ajouter des commentaires détaillés et de la documentation au code sélectionné. Disponible dans les actions de code (icône d'ampoule dans l'éditeur) et dans le menu contextuel de l'éditeur (clic droit sur le code sélectionné)."}}}, "advancedSystemPrompt": {"title": "Avancé : <PERSON><PERSON><PERSON><PERSON> le prompt système", "description": "Vous pouvez complètement remplacer le prompt système pour ce mode (en dehors de la définition du rôle et des instructions personnalisées) en créant un fichier à <span>.zhanlu/system-prompt-{{slug}}</span> dans votre espace de travail. Il s'agit d'une fonctionnalité très avancée qui contourne les garanties intégrées et les vérifications de cohérence (notamment concernant l'utilisation des outils), alors soyez prudent !"}, "createModeDialog": {"title": "Créer un nouveau mode", "close": "<PERSON><PERSON><PERSON>", "name": {"label": "Nom", "placeholder": "Entrez le nom du mode", "tooLong": "Le nom ne peut pas dépasser 20 caractères"}, "slug": {"label": "Slug", "description": "Le slug est utilisé dans les URL et les noms de fichiers. Il doit être en minuscules et ne contenir que des lettres, des chiffres et des tirets.", "tooLong": "Le slug ne peut pas dépasser 20 caractères"}, "saveLocation": {"label": "Emplacement d'enregistrement", "description": "Choisissez où enregistrer ce mode. Les modes spécifiques au projet ont priorité sur les modes globaux.", "global": {"label": "Global", "description": "Disponible dans tous les espaces de travail"}, "project": {"label": "Spécifique au projet (.z<PERSON><PERSON>)", "description": "Disponible uniquement dans cet espace de travail, a priorité sur le global"}}, "roleDefinition": {"label": "Définition du rôle", "description": "Définissez l'expertise et la personnalité de Zhanlu pour ce mode."}, "tools": {"label": "Outils disponibles", "description": "Sélectionnez quels outils ce mode peut utiliser."}, "description": {"label": "Description courte (pour humains)", "description": "Une brève description affichée dans le menu déroulant du sélecteur de mode."}, "customInstructions": {"label": "Instructions personnalisées (optionnel)", "description": "Ajoutez des directives comportementales spécifiques à ce mode."}, "buttons": {"cancel": "Annuler", "create": "<PERSON><PERSON><PERSON> le mode"}, "deleteMode": "Supprimer le mode"}, "allFiles": "tous les fichiers", "deleteMode": {"title": "Supprimer le mode", "message": "Êtes-vous sûr de vouloir supprimer le mode \"{{modeName}}\" ?", "rulesFolder": "Ce mode a un dossier de règles à {{folderPath}} qui sera également supprimé.", "descriptionNoRules": "Êtes-vous sûr de vouloir supprimer ce mode personnalisé ?", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler"}}