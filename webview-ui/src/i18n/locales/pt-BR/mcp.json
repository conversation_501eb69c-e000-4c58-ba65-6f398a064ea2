{"title": "Servidores MCP", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "marketplace": "Marketplace MCP", "description": "O <0>Model Context Protocol</0> permite a comunicação com servidores MCP em execução localmente que fornecem ferramentas e recursos adicionais para estender as capacidades do Roo. Você pode usar <1>servidores criados pela comunidade</1> ou pedir ao zhanlu para criar novas ferramentas específicas para seu fluxo de trabalho (por exemplo, \"adicionar uma ferramenta que obtém a documentação mais recente do npm\").", "instructions": "Instruções", "enableToggle": {"title": "Ativar servidores MCP", "description": "<PERSON>uando ativado, o Zhanlu poderá interagir com servidores MCP para funcionalidades avançadas. Se você não estiver usando MCP, pode desativar isso para reduzir o uso de tokens do Zhanlu."}, "enableServerCreation": {"title": "Ativar criação de servidores MCP", "description": "<PERSON>uando ativado, o Zhanlu pode ajudar você a criar novos servidores MCP por meio de comandos como \"adicionar uma nova ferramenta para...\". Se você não precisar criar servidores MCP, pode desativar isso para reduzir o uso de tokens do Zhanlu.", "hint": "Dica: Para reduzir os custos de tokens da API, desative esta configuração quando não estiver pedindo ao zhanlu para criar um novo servidor MCP."}, "editGlobalMCP": "Editar MCP Global", "editProjectMCP": "Editar MCP do Projeto", "whatIsMcp": "O que é o Servidor MCP", "learnMoreEditingSettings": "Saiba mais sobre como editar arquivos de configuração MCP", "tool": {"alwaysAllow": "Sempre permitir", "parameters": "Parâmetros", "noDescription": "Sem descrição", "togglePromptInclusion": "Alternar inclusão no prompt"}, "tabs": {"tools": "Ferramentas", "resources": "Recursos", "errors": "<PERSON><PERSON><PERSON>"}, "emptyState": {"noTools": "<PERSON>enhuma ferramenta encontrada", "noResources": "Nenhum recurso encontrado", "noErrors": "Nenhum erro encontrado"}, "networkTimeout": {"label": "Tempo limite de rede", "description": "Tempo máximo de espera por respostas do servidor", "options": {"15seconds": "15 segundos", "30seconds": "30 segundos", "1minute": "1 minuto", "5minutes": "5 minutos", "10minutes": "10 minutos", "15minutes": "15 minutos", "30minutes": "30 minutos", "60minutes": "60 minutos"}}, "deleteDialog": {"title": "Excluir servidor MCP", "description": "Tem certeza de que deseja excluir o servidor MCP \"{{serverName}}\"? Esta ação não pode ser desfeita.", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Excluir"}, "serverStatus": {"retrying": "Tentando novamente...", "retryConnection": "Tentar reconectar"}, "refreshMCP": "<PERSON><PERSON><PERSON><PERSON>", "execution": {"running": "Em execução", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "Erro"}}