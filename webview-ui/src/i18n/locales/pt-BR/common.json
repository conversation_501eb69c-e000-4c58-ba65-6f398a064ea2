{"errorBoundary": {"title": "<PERSON>go deu errado", "reportText": "Ajude-nos a melhorar relatando este erro em", "githubText": "nossa página de Issues no GitHub", "copyInstructions": "Copie e cole a seguinte mensagem de erro para incluí-la como parte do seu relatório:", "errorStack": "<PERSON><PERSON><PERSON>:", "componentStack": "Pilha de Componentes:"}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "answers": {"yes": "<PERSON>m", "no": "Não", "cancel": "<PERSON><PERSON><PERSON>", "remove": "Remover", "keep": "<PERSON><PERSON>"}, "ui": {"search_placeholder": "Pesquisar..."}, "mermaid": {"loading": "Gerando diagrama mermaid...", "render_error": "Não foi possível render<PERSON>r o diagrama", "file_media": "Visualização do processo", "code": "Mostrar código fonte", "buttons": {"zoom": "Zoom", "zoomIn": "Ampliar", "zoomOut": "Reduzir", "copy": "Copiar", "save": "<PERSON><PERSON> imagem", "viewCode": "<PERSON>er código", "viewDiagram": "Ver diagrama", "close": "<PERSON><PERSON><PERSON>"}, "modal": {"codeTitle": "<PERSON><PERSON><PERSON> Mermaid"}, "tabs": {"diagram": "Diagrama", "code": "Código"}, "feedback": {"imageCopied": "Imagem copiada para a área de transferência", "copyError": "Erro ao copiar imagem"}}, "file": {"errors": {"invalidDataUri": "Formato de URI de dados inválido", "copyingImage": "Erro ao copiar imagem: {{error}}", "openingImage": "Erro ao abrir imagem: {{error}}", "pathNotExists": "Caminho não existe: {{path}}", "couldNotOpen": "Não foi possível abrir o arquivo: {{error}}", "couldNotOpenGeneric": "Não foi possível abrir o arquivo!"}, "success": {"imageDataUriCopied": "URI de dados da imagem copiada para a área de transferência"}}, "confirmation": {"deleteMessage": "Excluir Mensagem", "deleteWarning": "Excluir esta mensagem irá excluir todas as mensagens subsequentes na conversa. Deseja prosseguir?", "editMessage": "<PERSON><PERSON>", "editWarning": "Editar esta mensagem irá excluir todas as mensagens subsequentes na conversa. Deseja prosseguir?", "proceed": "Prosseguir"}}