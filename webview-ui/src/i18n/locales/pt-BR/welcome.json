{"greeting": "Bem-vindo ao zhan<PERSON>!", "introduction": "Com uma variedade de Modos integrados e extensíveis, o zhanlu permite que você planeje, arquitete, codifique, depure e aumente sua produtividade como nunca antes.", "notice": "Para começar, esta extensão precisa de um provedor de API.", "start": "Vamos lá!", "routers": {"requesty": {"description": "Seu roteador LLM otimizado", "incentive": "$1 de crédit<PERSON> g<PERSON>"}, "openrouter": {"description": "Uma interface unificada para LLMs"}}, "chooseProvider": "Para fazer sua mágica, o zhanlu precisa de uma chave API.", "startRouter": "Recomendamos usar um roteador LLM:", "startCustom": "Ou você pode trazer sua própria chave API:", "telemetry": {"title": "Ajude a melhorar o Zhanlu", "anonymousTelemetry": "Envie dados de uso e erros anônimos para nos ajudar a corrigir bugs e melhorar a extensão. Nenhum código, texto ou informação pessoal é enviado.", "changeSettings": "Você sempre pode mudar isso na parte inferior das <settingsLink>configurações</settingsLink>", "settings": "configuraç<PERSON><PERSON>", "allow": "<PERSON><PERSON><PERSON>", "deny": "<PERSON><PERSON><PERSON>"}, "importSettings": "Importar configurações"}