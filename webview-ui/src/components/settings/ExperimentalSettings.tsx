import { HTMLAttributes } from "react"
import { FlaskConical } from "lucide-react"
import { VSCodeCheckbox } from "@vscode/webview-ui-toolkit/react"

import type { Experiments } from "@roo-code/types"

import { EXPERIMENT_IDS, experimentConfigsMap } from "@roo/experiments"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { cn } from "@src/lib/utils"

import { SetExperimentEnabled } from "./types"
import { SectionHeader } from "./SectionHeader"
import { Section } from "./Section"
import { ExperimentalFeature } from "./ExperimentalFeature"
import { RoomoteSettings } from "./RoomoteSettings"

type ExperimentalSettingsProps = HTMLAttributes<HTMLDivElement> & {
	experiments: Experiments
	setExperimentEnabled: SetExperimentEnabled
	// RoomoteSettings props
	roomoteApiUrl: string | undefined
	setRoomoteApiUrl: (value: string) => void
	areSettingsCommitted: boolean
}

export const ExperimentalSettings = ({
	experiments,
	setExperimentEnabled,
	roomoteApiUrl,
	setRoomoteApiUrl,
	areSettingsCommitted,
	className,
	...props
}: ExperimentalSettingsProps) => {
	const { t } = useAppTranslation()

	return (
		<div className={cn("flex flex-col gap-2", className)} {...props}>
			<SectionHeader>
				<div className="flex items-center gap-2">
					<FlaskConical className="w-4" />
					<div>{t("settings:sections.experimental")}</div>
				</div>
			</SectionHeader>

			<Section>
				{Object.entries(experimentConfigsMap)
					.filter(([key]) => key in EXPERIMENT_IDS)
					.map((config) => {
						const experimentKey = config[0] as keyof typeof EXPERIMENT_IDS
						const experimentId = EXPERIMENT_IDS[experimentKey]

						if (experimentKey === "MULTI_FILE_APPLY_DIFF") {
							return (
								<ExperimentalFeature
									key={experimentKey}
									experimentKey={experimentKey}
									enabled={experiments[EXPERIMENT_IDS.MULTI_FILE_APPLY_DIFF] ?? false}
									onChange={(enabled) =>
										setExperimentEnabled(EXPERIMENT_IDS.MULTI_FILE_APPLY_DIFF, enabled)
									}
								/>
							)
						}

						if (experimentKey === "ROOMOTE_AGENT") {
							return (
								<>
									<div key={experimentKey}>
										<div className="flex items-center gap-2">
											<VSCodeCheckbox
												checked={experiments[experimentId] ?? false}
												onChange={(e: any) =>
													setExperimentEnabled(experimentId, e.target.checked)
												}>
												<span className="font-medium">
													{t(`settings:experimental.${experimentKey}.name`)}
												</span>
											</VSCodeCheckbox>
										</div>
										<p className="text-vscode-descriptionForeground text-sm mt-0">
											{t(`settings:experimental.${experimentKey}.description`)}
										</p>
									</div>

									{experiments[experimentId] && (
										<div className="flex flex-col gap-3 pl-3 border-l-2 border-vscode-button-background">
											<RoomoteSettings
												roomoteApiUrl={roomoteApiUrl}
												setRoomoteApiUrl={setRoomoteApiUrl}
												areSettingsCommitted={areSettingsCommitted}
											/>
										</div>
									)}
								</>
							)
						}

						return (
							<ExperimentalFeature
								key={experimentKey}
								experimentKey={experimentKey}
								enabled={experiments[experimentId] ?? false}
								onChange={(enabled) => setExperimentEnabled(experimentId, enabled)}
							/>
						)
					})}
			</Section>
		</div>
	)
}
