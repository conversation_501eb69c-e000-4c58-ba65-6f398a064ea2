import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import clsx from "clsx"

import { buildDocLink } from "@src/utils/docLinks"

const tips = [
	// {
	// 	icon: "codicon-layout",
	// 	href: buildDocLink("basic-usage/using-modes", "tips"),
	// 	titleKey: "rooTips.architectMode.title",
	// 	descriptionKey: "rooTips.architectMode.description",
	// },
	{
		icon: "codicon-code",
		href: buildDocLink("basic-usage/using-modes", "tips"),
		titleKey: "rooTips.codeMode.title",
		descriptionKey: "rooTips.codeMode.description",
	},
	// {
	// 	icon: "codicon-beaker",
	// 	href: buildDocLink("basic-usage/using-modes", "tips"),
	// 	titleKey: "rooTips.testMode.title",
	// 	descriptionKey: "rooTips.testMode.description",
	// },
	// {
	// 	icon: "codicon-debug",
	// 	href: buildDocLink("basic-usage/using-modes", "tips"),
	// 	titleKey: "rooTips.projectFixMode.title",
	// 	descriptionKey: "rooTips.projectFixMode.description",
	// },
	// {
	// 	icon: "codicon-shield",
	// 	href: buildDocLink("basic-usage/using-modes", "tips"),
	// 	titleKey: "rooTips.sastMode.title",
	// 	descriptionKey: "rooTips.sastMode.description",
	// },
	// {
	// 	icon: "codicon-checklist",
	// 	href: buildDocLink("basic-usage/using-modes", "tips"),
	// 	titleKey: "rooTips.codeReviewMode.title",
	// 	descriptionKey: "rooTips.codeReviewMode.description",
	// },
	// {
	// 	icon: "codicon-book",
	// 	href: buildDocLink("basic-usage/using-modes", "tips"),
	// 	titleKey: "rooTips.readmeMode.title",
	// 	descriptionKey: "rooTips.readmeMode.description",
	// },
	{
		icon: "codicon-comment-discussion",
		href: buildDocLink("basic-usage/using-modes", "tips"),
		titleKey: "rooTips.simpleMode.title",
		descriptionKey: "rooTips.simpleMode.description",
	},
	// {
	// 	icon: "codicon-account",
	// 	href: buildDocLink("basic-usage/using-modes", "tips"),
	// 	titleKey: "rooTips.customizableModes.title",
	// 	descriptionKey: "rooTips.customizableModes.description",
	// },
	// {
	// 	icon: "codicon-list-tree",
	// 	href: buildDocLink("features/boomerang-tasks", "tips"),
	// 	titleKey: "rooTips.boomerangTasks.title",
	// 	descriptionKey: "rooTips.boomerangTasks.description",
	// },
	// {
	// 	icon: "codicon-save",
	// 	href: buildDocLink("features/sticky-models", "tips"),
	// 	titleKey: "rooTips.stickyModels.title",
	// 	descriptionKey: "rooTips.stickyModels.description",
	// },
	// {
	// 	icon: "codicon-tools",
	// 	href: buildDocLink("features/tools", "tips"),
	// 	titleKey: "rooTips.tools.title",
	// 	descriptionKey: "rooTips.tools.description",
	// },
]

interface RooTipsProps {
	cycle?: boolean
}

const RooTips = ({ cycle = false }: RooTipsProps) => {
	const { t } = useTranslation("chat")
	// Initialize with a random group starting index (multiple of 2)
	const [currentGroupIndex, setCurrentGroupIndex] = useState(
		Math.floor(Math.random() * Math.ceil(tips.length / 2)) * 2,
	)
	const [isFading, setIsFading] = useState(false)

	useEffect(() => {
		if (!cycle) return

		let timeoutId: NodeJS.Timeout | undefined = undefined
		const intervalId = setInterval(() => {
			setIsFading(true) // Start fade out
			timeoutId = setTimeout(() => {
				setCurrentGroupIndex((prevIndex) => (prevIndex + 2) % tips.length)
				setIsFading(false) // Start fade in
			}, 1000) // Fade duration
		}, 10500) // 10s display + 0.5s fade

		return () => {
			clearInterval(intervalId)
			if (timeoutId) {
				clearTimeout(timeoutId)
			}
		}
	}, [cycle])

	// Get current set of 2 tips when cycling
	const getCurrentCycleTips = () => {
		const tipsCount = tips.length
		return [tips[currentGroupIndex], tips[(currentGroupIndex + 1) % tipsCount]]
	}

	// Show more tips when not cycling, focusing on the mode tips
	const displayTips = cycle ? getCurrentCycleTips() : tips.slice(0, 8)

	return (
		<div
			className={clsx(
				"flex flex-col items-center justify-center px-5 py-2.5 gap-4",
				cycle && "h-auto overflow-visible m-5",
			)}>
			{/* If we need real estate, we show a compressed version of the tips. Otherwise, we expand it. */}
			{cycle ? (
				<>
					<div className="text-vscode-editor-foreground pb-1">你知道吗...</div>
					<div
						className={clsx(
							"flex flex-col gap-2 text-vscode-editor-foreground leading-tight font-vscode-font-family max-w-[320px] transition-opacity duration-1000 ease-in-out",
							isFading ? "opacity-0" : "opacity-100",
						)}>
						{displayTips.map((tip) => (
							<div key={tip.titleKey} className="flex items-start gap-2">
								<span className={`codicon ${tip.icon} flex-shrink-0 mt-0.5`}></span>
								<span className="leading-tight">
									<span className="text-blue-500">{t(tip.titleKey)}</span>: {t(tip.descriptionKey)}
								</span>
							</div>
						))}
					</div>
				</>
			) : (
				displayTips.map((tip) => (
					<div
						key={tip.titleKey}
						className="flex items-start gap-2 text-vscode-editor-foreground leading-tight font-vscode-font-family max-w-[320px]">
						<span className={`codicon ${tip.icon} flex-shrink-0 mt-0.5`}></span>
						<span className="leading-tight">
							<span className="text-blue-500">{t(tip.titleKey)}</span>: {t(tip.descriptionKey)}
						</span>
					</div>
				))
			)}
		</div>
	)
}

export default RooTips
